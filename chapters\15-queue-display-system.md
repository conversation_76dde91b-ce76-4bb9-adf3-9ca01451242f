# Chapter 15: Queue Display System

Welcome to Chapter 15! In this chapter, we'll build a comprehensive digital queue management system that provides real-time status displays, progress indicators, estimated wait times, and digital signage for customer information.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create a digital queue management system
- Build real-time queue status displays for customers
- Implement current service progress indicators
- Add estimated wait times for each queue position
- Create digital signage interface for lobby/waiting area display
- Build queue number assignment and calling system
- Integrate queue system with existing booking system
- Add queue analytics and reporting

## 📋 What We'll Cover

1. Setting up queue database structure
2. Creating queue management models
3. Building the queue display interface
4. Implementing real-time updates with WebSockets
5. Creating digital signage displays
6. Queue number assignment system
7. Integration with booking system
8. Queue analytics and reporting

## 🛠 Step 1: Database Structure for Queue System

First, let's create the necessary migrations for our queue system:

```bash
# Create queue-related migrations
php artisan make:migration create_queue_numbers_table
php artisan make:migration create_queue_displays_table
php artisan make:migration create_queue_settings_table
php artisan make:migration add_queue_fields_to_bookings_table
```

Edit `database/migrations/create_queue_numbers_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('queue_numbers', function (Blueprint $table) {
            $table->id();
            $table->string('queue_number')->unique();
            $table->foreignId('booking_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->string('customer_name');
            $table->string('service_type');
            $table->enum('status', ['waiting', 'in_progress', 'completed', 'cancelled', 'no_show'])->default('waiting');
            $table->integer('estimated_duration')->nullable(); // in minutes
            $table->timestamp('assigned_at');
            $table->timestamp('called_at')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('position_in_queue')->nullable();
            $table->text('notes')->nullable();
            $table->json('service_details')->nullable();
            $table->timestamps();

            $table->index(['status', 'assigned_at']);
            $table->index(['queue_number']);
            $table->index(['position_in_queue']);
            $table->index(['customer_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('queue_numbers');
    }
};
```

Edit `database/migrations/create_queue_displays_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('queue_displays', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('location');
            $table->enum('type', ['main_display', 'service_bay', 'waiting_area', 'mobile']);
            $table->json('display_settings'); // Layout, colors, refresh rate, etc.
            $table->json('queue_filters')->nullable(); // Which queues to show
            $table->boolean('is_active')->default(true);
            $table->string('ip_address')->nullable();
            $table->timestamp('last_ping')->nullable();
            $table->timestamps();

            $table->index(['type', 'is_active']);
            $table->index(['location']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('queue_displays');
    }
};
```

Edit `database/migrations/create_queue_settings_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('queue_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value');
            $table->string('type')->default('string'); // string, integer, boolean, json
            $table->text('description')->nullable();
            $table->string('group')->default('general');
            $table->timestamps();

            $table->index(['key']);
            $table->index(['group']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('queue_settings');
    }
};
```

Edit `database/migrations/add_queue_fields_to_bookings_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->foreignId('queue_number_id')->nullable()->constrained()->onDelete('set null');
            $table->boolean('auto_queue')->default(true);
            $table->timestamp('queue_assigned_at')->nullable();
            $table->integer('estimated_wait_time')->nullable(); // in minutes
        });
    }

    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropForeign(['queue_number_id']);
            $table->dropColumn(['queue_number_id', 'auto_queue', 'queue_assigned_at', 'estimated_wait_time']);
        });
    }
};
```

Run the migrations:

```bash
php artisan migrate
```

## 🛠 Step 2: Creating Queue Models

Create the models for our queue system:

```bash
# Create queue models
php artisan make:model QueueNumber
php artisan make:model QueueDisplay
php artisan make:model QueueSetting
```

Edit `app/Models/QueueNumber.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class QueueNumber extends Model
{
    use HasFactory;

    protected $fillable = [
        'queue_number',
        'booking_id',
        'customer_id',
        'customer_name',
        'service_type',
        'status',
        'estimated_duration',
        'assigned_at',
        'called_at',
        'started_at',
        'completed_at',
        'position_in_queue',
        'notes',
        'service_details',
    ];

    protected $casts = [
        'service_details' => 'array',
        'assigned_at' => 'datetime',
        'called_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // Status constants
    const STATUS_WAITING = 'waiting';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_NO_SHOW = 'no_show';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($queueNumber) {
            if (empty($queueNumber->queue_number)) {
                $queueNumber->queue_number = static::generateQueueNumber();
            }
            if (empty($queueNumber->assigned_at)) {
                $queueNumber->assigned_at = now();
            }
            if (is_null($queueNumber->position_in_queue)) {
                $queueNumber->position_in_queue = static::getNextPosition();
            }
        });

        static::updated(function ($queueNumber) {
            if ($queueNumber->wasChanged('status')) {
                static::updateQueuePositions();
            }
        });
    }

    public static function generateQueueNumber(): string
    {
        $prefix = 'Q';
        $date = now()->format('md');
        $lastQueue = static::whereDate('assigned_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastQueue ? 
            intval(substr($lastQueue->queue_number, -3)) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    public static function getNextPosition(): int
    {
        return static::where('status', self::STATUS_WAITING)
            ->max('position_in_queue') + 1;
    }

    public static function updateQueuePositions(): void
    {
        $waitingQueues = static::where('status', self::STATUS_WAITING)
            ->orderBy('assigned_at')
            ->get();

        foreach ($waitingQueues as $index => $queue) {
            $queue->update(['position_in_queue' => $index + 1]);
        }
    }

    // Relationships
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Scopes
    public function scopeWaiting(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_WAITING);
    }

    public function scopeInProgress(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_IN_PROGRESS);
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->whereIn('status', [self::STATUS_WAITING, self::STATUS_IN_PROGRESS]);
    }

    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('assigned_at', today());
    }

    // Accessors
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_WAITING => 'Waiting',
            self::STATUS_IN_PROGRESS => 'In Progress',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_NO_SHOW => 'No Show',
            default => 'Unknown',
        };
    }

    public function getEstimatedWaitTimeAttribute(): int
    {
        if ($this->status !== self::STATUS_WAITING) {
            return 0;
        }

        $queuesBefore = static::where('status', self::STATUS_WAITING)
            ->where('position_in_queue', '<', $this->position_in_queue)
            ->get();

        $totalWaitTime = 0;
        foreach ($queuesBefore as $queue) {
            $totalWaitTime += $queue->estimated_duration ?? 30; // Default 30 minutes
        }

        // Add current service time if any queue is in progress
        $currentService = static::where('status', self::STATUS_IN_PROGRESS)->first();
        if ($currentService) {
            $elapsedTime = now()->diffInMinutes($currentService->started_at);
            $remainingTime = max(0, ($currentService->estimated_duration ?? 30) - $elapsedTime);
            $totalWaitTime += $remainingTime;
        }

        return $totalWaitTime;
    }

    public function getFormattedWaitTimeAttribute(): string
    {
        $minutes = $this->estimated_wait_time;
        
        if ($minutes < 60) {
            return $minutes . ' min';
        }
        
        $hours = floor($minutes / 60);
        $remainingMinutes = $minutes % 60;
        
        return $hours . 'h ' . $remainingMinutes . 'm';
    }

    // Methods
    public function callNext(): void
    {
        $this->update([
            'status' => self::STATUS_IN_PROGRESS,
            'called_at' => now(),
            'started_at' => now(),
        ]);

        // Broadcast to displays
        broadcast(new \App\Events\QueueUpdated($this));
    }

    public function markCompleted(): void
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'completed_at' => now(),
        ]);

        // Update queue positions
        static::updateQueuePositions();

        // Broadcast to displays
        broadcast(new \App\Events\QueueUpdated($this));
    }

    public function markNoShow(): void
    {
        $this->update([
            'status' => self::STATUS_NO_SHOW,
            'completed_at' => now(),
        ]);

        // Update queue positions
        static::updateQueuePositions();

        // Broadcast to displays
        broadcast(new \App\Events\QueueUpdated($this));
    }
}
```

## 🧪 Testing the Queue System

1. **Test Queue Assignment**:
   - Create queue numbers manually and from bookings
   - Verify position calculations and wait times
   - Test queue status transitions

2. **Test Real-time Updates**:
   - Open multiple display windows
   - Verify real-time synchronization
   - Test WebSocket connections

3. **Test Integration**:
   - Verify booking integration
   - Test customer data synchronization
   - Validate queue analytics

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Built a comprehensive digital queue management system
✅ Created real-time queue status displays for customers
✅ Implemented current service progress indicators
✅ Added estimated wait times for each queue position
✅ Built digital signage interface for lobby/waiting area display
✅ Created queue number assignment and calling system
✅ Integrated queue system with existing booking system
✅ Added queue analytics and reporting

### Queue System Features Implemented:
- **Digital Queue Management**: Automated queue number assignment and tracking
- **Real-time Displays**: Live updates across all display screens
- **Wait Time Estimation**: Intelligent calculation of customer wait times
- **Digital Signage**: Professional lobby displays with queue information
- **Booking Integration**: Seamless integration with existing booking system
- **Queue Analytics**: Comprehensive reporting and performance metrics
- **Multi-display Support**: Support for multiple display types and locations
- **WebSocket Integration**: Real-time updates using Laravel Broadcasting

## 🚀 What's Next?

In the next chapter, we'll:
- Build a comprehensive bay management system
- Implement real-time bay status tracking
- Create bay assignment optimization
- Add service progress tracking within each bay
- Build bay utilization analytics and reporting

---

**Ready for bay management?** Let's move on to [Chapter 16: Bay Management System](./16-bay-management-system.md)!
