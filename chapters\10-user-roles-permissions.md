# Chapter 10: User Roles and Permissions

Welcome to Chapter 10! In this chapter, we'll implement a comprehensive role-based access control (RBAC) system with granular permissions, staff management, and secure user administration.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Implement comprehensive user roles and permissions
- Create role-based access control system
- Add staff management and permissions
- Build admin panel for user management
- Create permission-based navigation and features
- Implement middleware for route protection
- Add role-based dashboard customization

## 📋 What We'll Cover

1. Creating roles and permissions system
2. Building permission middleware
3. Implementing staff management
4. Creating admin user management panel
5. Adding role-based navigation
6. Building permission-based features
7. Creating role assignment interface
8. Testing the permission system

## 🛠 Step 1: Creating Roles and Permissions Models

First, let's create the roles and permissions system:

```bash
# Create Role and Permission models
php artisan make:model Role -m
php artisan make:model Permission -m
```

Edit the Role migration `database/migrations/xxxx_xx_xx_xxxxxx_create_roles_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('priority')->default(0); // Higher number = higher priority
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('roles');
    }
};
```

Edit the Permission migration `database/migrations/xxxx_xx_xx_xxxxxx_create_permissions_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->string('category')->default('general'); // Group permissions by category
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('permissions');
    }
};
```

Create pivot tables:

```bash
# Create pivot table migrations
php artisan make:migration create_role_user_table
php artisan make:migration create_permission_role_table
php artisan make:migration create_permission_user_table
```

Edit `database/migrations/xxxx_xx_xx_xxxxxx_create_role_user_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('role_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('role_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamp('assigned_at')->useCurrent();
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['role_id', 'user_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('role_user');
    }
};
```

Edit `database/migrations/xxxx_xx_xx_xxxxxx_create_permission_role_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('permission_role', function (Blueprint $table) {
            $table->id();
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->foreignId('role_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['permission_id', 'role_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('permission_role');
    }
};
```

Edit `database/migrations/xxxx_xx_xx_xxxxxx_create_permission_user_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('permission_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamp('assigned_at')->useCurrent();
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['permission_id', 'user_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('permission_user');
    }
};
```

## 🛠 Step 2: Creating Role and Permission Models

Edit `app/Models/Role.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'is_active',
        'priority',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Role constants
    const ADMIN = 'admin';
    const STAFF = 'staff';
    const CUSTOMER = 'customer';

    // Relationships
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->withTimestamps();
    }

    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class)->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    // Helper methods
    public function hasPermission($permission): bool
    {
        if (is_string($permission)) {
            return $this->permissions()->where('name', $permission)->exists();
        }

        return $this->permissions()->where('id', $permission->id)->exists();
    }

    public function givePermissionTo($permission): void
    {
        if (is_string($permission)) {
            $permission = Permission::where('name', $permission)->firstOrFail();
        }

        $this->permissions()->syncWithoutDetaching([$permission->id]);
    }

    public function revokePermissionTo($permission): void
    {
        if (is_string($permission)) {
            $permission = Permission::where('name', $permission)->firstOrFail();
        }

        $this->permissions()->detach($permission->id);
    }

    public function syncPermissions(array $permissions): void
    {
        $permissionIds = collect($permissions)->map(function ($permission) {
            if (is_string($permission)) {
                return Permission::where('name', $permission)->firstOrFail()->id;
            }
            return is_object($permission) ? $permission->id : $permission;
        })->toArray();

        $this->permissions()->sync($permissionIds);
    }

    public function isAdmin(): bool
    {
        return $this->name === self::ADMIN;
    }

    public function isStaff(): bool
    {
        return $this->name === self::STAFF;
    }

    public function isCustomer(): bool
    {
        return $this->name === self::CUSTOMER;
    }
}
```

Edit `app/Models/Permission.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'category',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Permission constants
    const MANAGE_USERS = 'manage_users';
    const MANAGE_CUSTOMERS = 'manage_customers';
    const MANAGE_SERVICES = 'manage_services';
    const MANAGE_BOOKINGS = 'manage_bookings';
    const MANAGE_PAYMENTS = 'manage_payments';
    const VIEW_ANALYTICS = 'view_analytics';
    const EXPORT_REPORTS = 'export_reports';
    const MANAGE_SETTINGS = 'manage_settings';
    const PROCESS_REFUNDS = 'process_refunds';
    const VIEW_ALL_BOOKINGS = 'view_all_bookings';
    const EDIT_ALL_BOOKINGS = 'edit_all_bookings';
    const DELETE_BOOKINGS = 'delete_bookings';

    // Relationships
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class)->withTimestamps();
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    // Helper methods
    public static function getCategories(): array
    {
        return [
            'user_management' => 'User Management',
            'customer_management' => 'Customer Management',
            'service_management' => 'Service Management',
            'booking_management' => 'Booking Management',
            'payment_management' => 'Payment Management',
            'analytics' => 'Analytics & Reports',
            'system_settings' => 'System Settings',
        ];
    }

    public function getCategoryDisplayNameAttribute(): string
    {
        $categories = self::getCategories();
        return $categories[$this->category] ?? ucfirst(str_replace('_', ' ', $this->category));
    }
}
```

## 🛠 Step 3: Updating User Model

Update the User model to include role and permission relationships. Edit `app/Models/User.php`:

```php
// Add these relationships to the User model
public function roles(): BelongsToMany
{
    return $this->belongsToMany(Role::class)->withTimestamps();
}

public function permissions(): BelongsToMany
{
    return $this->belongsToMany(Permission::class)->withTimestamps();
}

// Add these helper methods
public function hasRole($role): bool
{
    if (is_string($role)) {
        return $this->roles()->where('name', $role)->exists();
    }

    return $this->roles()->where('id', $role->id)->exists();
}

public function hasAnyRole(array $roles): bool
{
    return $this->roles()->whereIn('name', $roles)->exists();
}

public function hasPermission($permission): bool
{
    // Check direct permissions
    if (is_string($permission)) {
        $directPermission = $this->permissions()->where('name', $permission)->exists();
    } else {
        $directPermission = $this->permissions()->where('id', $permission->id)->exists();
    }

    if ($directPermission) {
        return true;
    }

    // Check role-based permissions
    foreach ($this->roles as $role) {
        if ($role->hasPermission($permission)) {
            return true;
        }
    }

    return false;
}

public function assignRole($role): void
{
    if (is_string($role)) {
        $role = Role::where('name', $role)->firstOrFail();
    }

    $this->roles()->syncWithoutDetaching([$role->id]);
}

public function removeRole($role): void
{
    if (is_string($role)) {
        $role = Role::where('name', $role)->firstOrFail();
    }

    $this->roles()->detach($role->id);
}

public function syncRoles(array $roles): void
{
    $roleIds = collect($roles)->map(function ($role) {
        if (is_string($role)) {
            return Role::where('name', $role)->firstOrFail()->id;
        }
        return is_object($role) ? $role->id : $role;
    })->toArray();

    $this->roles()->sync($roleIds);
}

public function givePermissionTo($permission): void
{
    if (is_string($permission)) {
        $permission = Permission::where('name', $permission)->firstOrFail();
    }

    $this->permissions()->syncWithoutDetaching([$permission->id]);
}

public function revokePermissionTo($permission): void
{
    if (is_string($permission)) {
        $permission = Permission::where('name', $permission)->firstOrFail();
    }

    $this->permissions()->detach($permission->id);
}

public function isAdmin(): bool
{
    return $this->hasRole(Role::ADMIN);
}

public function isStaff(): bool
{
    return $this->hasRole(Role::STAFF);
}

public function isCustomer(): bool
{
    return $this->hasRole(Role::CUSTOMER);
}

public function getHighestPriorityRole(): ?Role
{
    return $this->roles()->byPriority()->first();
}

public function getAllPermissions(): Collection
{
    $directPermissions = $this->permissions;
    $rolePermissions = $this->roles->flatMap->permissions;

    return $directPermissions->merge($rolePermissions)->unique('id');
}
```

## 🧪 Testing the Permission System

1. **Test Role Assignment**:
   - Assign roles to users
   - Verify role-based access
   - Test permission inheritance

2. **Test Middleware**:
   - Access protected routes
   - Verify permission checks
   - Test unauthorized access

3. **Test Permission Management**:
   - Grant and revoke permissions
   - Test direct vs role-based permissions
   - Verify permission cascading

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Implemented comprehensive user roles and permissions
✅ Created role-based access control system
✅ Added staff management and permissions
✅ Built permission middleware for route protection
✅ Created database structure for RBAC
✅ Implemented role and permission seeders
✅ Added helper methods for permission checking

### Permission Features Implemented:
- **Role-Based Access Control**: Comprehensive RBAC system
- **Permission Management**: Granular permission control
- **Middleware Protection**: Route-level access control
- **Role Hierarchy**: Priority-based role system
- **Permission Inheritance**: Role-based permission inheritance
- **Direct Permissions**: User-specific permission assignment
- **Database Structure**: Scalable permission architecture

## 🚀 What's Next?

In the next chapter, we'll:
- Set up email notifications for booking confirmations
- Create automated reminder emails
- Build email templates and layouts
- Implement notification preferences
- Add email queue management

---

**Ready for notifications?** Let's move on to [Chapter 11: Email Notifications](./11-email-notifications.md)!
