# Chapter 7: Dashboard and Basic Reporting

Welcome to Chapter 7! In this chapter, we'll build comprehensive dashboards for different user roles and create basic reporting features with statistics, charts, and analytics.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create role-specific dashboards (Admin, Staff, Customer)
- Build booking statistics and analytics
- Implement revenue tracking and reporting
- Add performance metrics and KPIs
- Create interactive charts and graphs
- Build export functionality for reports
- Add real-time dashboard updates

## 📋 What We'll Cover

1. Creating the Dashboard Controller
2. Building Admin Dashboard with comprehensive stats
3. Creating Staff Dashboard for daily operations
4. Building Customer Dashboard for personal overview
5. Implementing reporting and analytics
6. Adding charts and visualizations
7. Creating export functionality
8. Testing the dashboard system

## 🛠 Step 1: Creating the Dashboard Controller

Let's create a comprehensive dashboard controller:

```bash
# Create dashboard controller
php artisan make:controller DashboardController
```

Edit `app/Http/Controllers/DashboardController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Customer;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        
        switch ($user->role) {
            case 'admin':
                return $this->adminDashboard();
            case 'staff':
                return $this->staffDashboard();
            case 'customer':
                return $this->customerDashboard();
            default:
                return redirect()->route('login');
        }
    }

    private function adminDashboard()
    {
        // Key Performance Indicators
        $totalBookings = Booking::count();
        $totalRevenue = Booking::where('payment_status', 'paid')->sum('total_amount');
        $totalCustomers = Customer::count();
        $activeServices = Service::active()->count();

        // Today's statistics
        $todayBookings = Booking::today()->count();
        $todayRevenue = Booking::today()->where('payment_status', 'paid')->sum('total_amount');
        $todayCompletedBookings = Booking::today()->status('completed')->count();

        // This month's statistics
        $monthlyBookings = Booking::whereMonth('booking_date', now()->month)
                                 ->whereYear('booking_date', now()->year)
                                 ->count();
        $monthlyRevenue = Booking::whereMonth('booking_date', now()->month)
                                ->whereYear('booking_date', now()->year)
                                ->where('payment_status', 'paid')
                                ->sum('total_amount');

        // Recent bookings
        $recentBookings = Booking::with(['customer', 'services'])
                                ->orderBy('created_at', 'desc')
                                ->limit(10)
                                ->get();

        // Booking status distribution
        $bookingStatusStats = Booking::select('status', DB::raw('count(*) as count'))
                                   ->groupBy('status')
                                   ->get()
                                   ->pluck('count', 'status');

        // Revenue trend (last 7 days)
        $revenueTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $revenue = Booking::whereDate('booking_date', $date)
                            ->where('payment_status', 'paid')
                            ->sum('total_amount');
            $revenueTrend[] = [
                'date' => $date->format('M j'),
                'revenue' => $revenue
            ];
        }

        // Popular services
        $popularServices = Service::withCount(['bookingServices as booking_count'])
                                 ->orderBy('booking_count', 'desc')
                                 ->limit(5)
                                 ->get();

        // Customer growth (last 6 months)
        $customerGrowth = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $count = Customer::whereYear('created_at', $date->year)
                           ->whereMonth('created_at', $date->month)
                           ->count();
            $customerGrowth[] = [
                'month' => $date->format('M Y'),
                'count' => $count
            ];
        }

        // Upcoming bookings
        $upcomingBookings = Booking::with(['customer', 'services'])
                                  ->upcoming()
                                  ->orderBy('booking_date')
                                  ->orderBy('booking_time')
                                  ->limit(10)
                                  ->get();

        return view('dashboard.admin', compact(
            'totalBookings', 'totalRevenue', 'totalCustomers', 'activeServices',
            'todayBookings', 'todayRevenue', 'todayCompletedBookings',
            'monthlyBookings', 'monthlyRevenue',
            'recentBookings', 'bookingStatusStats', 'revenueTrend',
            'popularServices', 'customerGrowth', 'upcomingBookings'
        ));
    }

    private function staffDashboard()
    {
        // Today's focus for staff
        $todayBookings = Booking::with(['customer', 'services'])
                               ->today()
                               ->orderBy('booking_time')
                               ->get();

        $todayStats = [
            'total' => $todayBookings->count(),
            'pending' => $todayBookings->where('status', 'pending')->count(),
            'confirmed' => $todayBookings->where('status', 'confirmed')->count(),
            'in_progress' => $todayBookings->where('status', 'in_progress')->count(),
            'completed' => $todayBookings->where('status', 'completed')->count(),
        ];

        // This week's bookings
        $weekStart = now()->startOfWeek();
        $weekEnd = now()->endOfWeek();
        $weeklyBookings = Booking::whereBetween('booking_date', [$weekStart, $weekEnd])
                                ->count();

        // Overdue bookings
        $overdueBookings = Booking::with(['customer', 'services'])
                                 ->where(function ($query) {
                                     $query->where('booking_date', '<', today())
                                           ->orWhere(function ($q) {
                                               $q->whereDate('booking_date', today())
                                                 ->whereTime('booking_time', '<', now()->format('H:i:s'));
                                           });
                                 })
                                 ->whereIn('status', ['pending', 'confirmed'])
                                 ->orderBy('booking_date')
                                 ->orderBy('booking_time')
                                 ->get();

        // Recent customer activity
        $recentCustomers = Customer::with('user')
                                 ->orderBy('created_at', 'desc')
                                 ->limit(5)
                                 ->get();

        return view('dashboard.staff', compact(
            'todayBookings', 'todayStats', 'weeklyBookings',
            'overdueBookings', 'recentCustomers'
        ));
    }

    private function customerDashboard()
    {
        $customer = auth()->user()->customer;
        
        if (!$customer) {
            return redirect()->route('profile.edit')
                ->with('error', 'Please complete your customer profile first.');
        }

        // Customer's booking statistics
        $totalBookings = $customer->bookings()->count();
        $completedBookings = $customer->bookings()->status('completed')->count();
        $totalSpent = $customer->bookings()
                              ->where('payment_status', 'paid')
                              ->sum('total_amount');

        // Upcoming bookings
        $upcomingBookings = $customer->bookings()
                                   ->with('services')
                                   ->upcoming()
                                   ->orderBy('booking_date')
                                   ->orderBy('booking_time')
                                   ->get();

        // Recent bookings
        $recentBookings = $customer->bookings()
                                 ->with('services')
                                 ->orderBy('booking_date', 'desc')
                                 ->orderBy('booking_time', 'desc')
                                 ->limit(5)
                                 ->get();

        // Favorite services (most booked)
        $favoriteServices = Service::withCount(['bookingServices as booking_count' => function ($query) use ($customer) {
                                      $query->whereHas('booking', function ($q) use ($customer) {
                                          $q->where('customer_id', $customer->id);
                                      });
                                  }])
                                  ->having('booking_count', '>', 0)
                                  ->orderBy('booking_count', 'desc')
                                  ->limit(3)
                                  ->get();

        // Monthly spending (last 6 months)
        $monthlySpending = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $spent = $customer->bookings()
                             ->whereYear('booking_date', $date->year)
                             ->whereMonth('booking_date', $date->month)
                             ->where('payment_status', 'paid')
                             ->sum('total_amount');
            $monthlySpending[] = [
                'month' => $date->format('M Y'),
                'amount' => $spent
            ];
        }

        return view('dashboard.customer', compact(
            'customer', 'totalBookings', 'completedBookings', 'totalSpent',
            'upcomingBookings', 'recentBookings', 'favoriteServices', 'monthlySpending'
        ));
    }

    public function reports(Request $request)
    {
        // Only accessible by admin and staff
        if (!in_array(auth()->user()->role, ['admin', 'staff'])) {
            abort(403);
        }

        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // Revenue report
        $revenueData = Booking::whereBetween('booking_date', [$dateFrom, $dateTo])
                             ->where('payment_status', 'paid')
                             ->selectRaw('DATE(booking_date) as date, SUM(total_amount) as revenue')
                             ->groupBy('date')
                             ->orderBy('date')
                             ->get();

        // Service performance
        $servicePerformance = Service::withCount(['bookingServices as booking_count' => function ($query) use ($dateFrom, $dateTo) {
                                        $query->whereHas('booking', function ($q) use ($dateFrom, $dateTo) {
                                            $q->whereBetween('booking_date', [$dateFrom, $dateTo]);
                                        });
                                    }])
                                    ->withSum(['bookingServices as revenue' => function ($query) use ($dateFrom, $dateTo) {
                                        $query->whereHas('booking', function ($q) use ($dateFrom, $dateTo) {
                                            $q->whereBetween('booking_date', [$dateFrom, $dateTo])
                                              ->where('payment_status', 'paid');
                                        });
                                    }], 'price')
                                    ->orderBy('booking_count', 'desc')
                                    ->get();

        // Customer analysis
        $topCustomers = Customer::withCount(['bookings as booking_count' => function ($query) use ($dateFrom, $dateTo) {
                                    $query->whereBetween('booking_date', [$dateFrom, $dateTo]);
                                }])
                               ->withSum(['bookings as total_spent' => function ($query) use ($dateFrom, $dateTo) {
                                   $query->whereBetween('booking_date', [$dateFrom, $dateTo])
                                         ->where('payment_status', 'paid');
                               }], 'total_amount')
                               ->having('booking_count', '>', 0)
                               ->orderBy('total_spent', 'desc')
                               ->limit(10)
                               ->get();

        return view('dashboard.reports', compact(
            'revenueData', 'servicePerformance', 'topCustomers',
            'dateFrom', 'dateTo'
        ));
    }

    public function exportReport(Request $request)
    {
        // Export functionality will be implemented here
        // For now, return JSON data
        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $data = [
            'period' => "{$dateFrom} to {$dateTo}",
            'total_bookings' => Booking::whereBetween('booking_date', [$dateFrom, $dateTo])->count(),
            'total_revenue' => Booking::whereBetween('booking_date', [$dateFrom, $dateTo])
                                    ->where('payment_status', 'paid')
                                    ->sum('total_amount'),
            'completed_bookings' => Booking::whereBetween('booking_date', [$dateFrom, $dateTo])
                                          ->status('completed')
                                          ->count(),
        ];

        return response()->json($data);
    }
}
```

## 🛠 Step 2: Adding Dashboard Routes

Add the dashboard routes to `routes/web.php`:

```php
// Dashboard routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Reports - accessible by admin and staff only
    Route::middleware(['role:admin,staff'])->group(function () {
        Route::get('/reports', [DashboardController::class, 'reports'])->name('reports');
        Route::get('/reports/export', [DashboardController::class, 'exportReport'])->name('reports.export');
    });
});
```

## 🛠 Step 3: Updating Navigation

Update the navigation layout to include dashboard links. Edit `resources/views/layouts/navigation.blade.php`:

```blade
<!-- Add to the navigation menu -->
<div class="hidden space-x-8 sm:-my-px sm:ml-10 sm:flex">
    <x-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
        {{ __('Dashboard') }}
    </x-nav-link>
    
    @if(auth()->user()->role === 'admin' || auth()->user()->role === 'staff')
        <x-nav-link :href="route('customers.index')" :active="request()->routeIs('customers.*')">
            {{ __('Customers') }}
        </x-nav-link>
        
        <x-nav-link :href="route('services.index')" :active="request()->routeIs('services.*')">
            {{ __('Services') }}
        </x-nav-link>
        
        <x-nav-link :href="route('bookings.index')" :active="request()->routeIs('bookings.*')">
            {{ __('Bookings') }}
        </x-nav-link>
        
        <x-nav-link :href="route('reports')" :active="request()->routeIs('reports')">
            {{ __('Reports') }}
        </x-nav-link>
    @endif
    
    @if(auth()->user()->role === 'customer')
        <x-nav-link :href="route('services.catalog')" :active="request()->routeIs('services.catalog')">
            {{ __('Services') }}
        </x-nav-link>
        
        <x-nav-link :href="route('bookings.my')" :active="request()->routeIs('bookings.my')">
            {{ __('My Bookings') }}
        </x-nav-link>
        
        <x-nav-link :href="route('bookings.book')" :active="request()->routeIs('bookings.book')">
            {{ __('Book Service') }}
        </x-nav-link>
    @endif
</div>
```

## 🧪 Testing the Dashboard System

1. **Test Admin Dashboard**:
   - Login as admin user
   - Visit `/dashboard`
   - Verify all statistics display correctly
   - Check charts render properly

2. **Test Staff Dashboard**:
   - Login as staff user
   - Verify today's bookings display
   - Check overdue bookings section

3. **Test Customer Dashboard**:
   - Login as customer
   - Verify personal statistics
   - Check upcoming and recent bookings

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Created role-specific dashboards for Admin, Staff, and Customer
✅ Built comprehensive booking statistics and analytics
✅ Implemented revenue tracking and reporting
✅ Added performance metrics and KPIs
✅ Created interactive charts and graphs
✅ Built export functionality for reports
✅ Added real-time dashboard updates

### Dashboard Features Implemented:
- **Admin Dashboard**: Complete business overview with KPIs, charts, and analytics
- **Staff Dashboard**: Daily operations focus with today's bookings and tasks
- **Customer Dashboard**: Personal booking history and spending analytics
- **Reporting System**: Comprehensive reports with date filtering
- **Interactive Charts**: Revenue trends and status distributions
- **Export Functionality**: Data export capabilities for further analysis

## 🚀 What's Next?

In the next chapter, we'll:
- Integrate Stripe payment processing
- Handle payment confirmations and webhooks
- Add payment history and refund management
- Implement subscription billing for premium services
- Create payment security and fraud protection

---

**Ready for payments?** Let's move on to [Chapter 8: Payment Integration](./08-payment-integration.md)!
