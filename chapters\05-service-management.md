# Chapter 5: Service Management

Welcome to Chapter 5! In this chapter, we'll build a comprehensive service management system that allows administrators to manage car wash services, pricing, and service categories.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create a Service model with relationships to ServiceCategory
- Build a complete service management interface
- Implement CRUD operations for services and categories
- Add service pricing and duration management
- Create service availability and scheduling features
- Implement service image uploads
- Build a public service catalog for customers

## 📋 What We'll Cover

1. Enhancing the Service model with advanced features
2. Creating service category management
3. Building service controller with CRUD operations
4. Creating service views and forms
5. Implementing service image uploads
6. Adding service availability management
7. Creating a public service catalog
8. Testing the service management system

## 🛠 Step 1: Enhancing the Service Model

Let's enhance our existing Service model with more advanced features. First, let's update the Service model `app/Models/Service.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Service extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'service_category_id',
        'name',
        'description',
        'short_description',
        'price',
        'duration_minutes',
        'is_active',
        'is_featured',
        'image',
        'requirements',
        'benefits',
        'popularity_score',
        'min_advance_booking_hours',
        'max_advance_booking_days',
        'available_days',
        'available_time_slots',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'duration_minutes' => 'integer',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'requirements' => 'array',
        'benefits' => 'array',
        'popularity_score' => 'integer',
        'min_advance_booking_hours' => 'integer',
        'max_advance_booking_days' => 'integer',
        'available_days' => 'array', // ['monday', 'tuesday', etc.]
        'available_time_slots' => 'array', // ['09:00-10:00', '10:00-11:00', etc.]
    ];

    // Relationships
    public function serviceCategory(): BelongsTo
    {
        return $this->belongsTo(ServiceCategory::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    public function bookingServices(): BelongsToMany
    {
        return $this->belongsToMany(Booking::class, 'booking_services')
                    ->withPivot(['quantity', 'price', 'notes'])
                    ->withTimestamps();
    }

    // Accessors
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    public function getFormattedDurationAttribute(): string
    {
        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;
        
        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }

    public function getEstimatedEndTimeAttribute(): string
    {
        // This will be used when booking to calculate end time
        return date('H:i', strtotime("+{$this->duration_minutes} minutes"));
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('service_category_id', $categoryId);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('short_description', 'like', "%{$search}%");
        });
    }

    public function scopePopular($query)
    {
        return $query->orderBy('popularity_score', 'desc');
    }

    public function scopePriceRange($query, $minPrice = null, $maxPrice = null)
    {
        if ($minPrice !== null) {
            $query->where('price', '>=', $minPrice);
        }
        
        if ($maxPrice !== null) {
            $query->where('price', '<=', $maxPrice);
        }
        
        return $query;
    }

    // Helper methods
    public function isAvailableOnDay($day): bool
    {
        return in_array(strtolower($day), $this->available_days ?? []);
    }

    public function getAvailableTimeSlots(): array
    {
        return $this->available_time_slots ?? [];
    }

    public function canBeBookedAt($dateTime): bool
    {
        $dayName = strtolower($dateTime->format('l'));
        $timeSlot = $dateTime->format('H:i');
        
        // Check if service is available on this day
        if (!$this->isAvailableOnDay($dayName)) {
            return false;
        }
        
        // Check advance booking requirements
        $hoursUntilBooking = now()->diffInHours($dateTime);
        
        if ($hoursUntilBooking < $this->min_advance_booking_hours) {
            return false;
        }
        
        $daysUntilBooking = now()->diffInDays($dateTime);
        
        if ($daysUntilBooking > $this->max_advance_booking_days) {
            return false;
        }
        
        return true;
    }

    public function incrementPopularity(): void
    {
        $this->increment('popularity_score');
    }

    // Boot method
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($service) {
            if (!isset($service->popularity_score)) {
                $service->popularity_score = 0;
            }
        });
    }
}
```

## 🛠 Step 2: Creating Service Migration Updates

Let's create a migration to add the new fields to our services table:

```bash
# Create migration to add new service fields
php artisan make:migration add_advanced_fields_to_services_table --table=services
```

Edit the migration file `database/migrations/xxxx_xx_xx_xxxxxx_add_advanced_fields_to_services_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->text('short_description')->nullable()->after('description');
            $table->boolean('is_featured')->default(false)->after('is_active');
            $table->string('image')->nullable()->after('is_featured');
            $table->json('requirements')->nullable()->after('image'); // What customer needs to bring/know
            $table->json('benefits')->nullable()->after('requirements'); // Service benefits
            $table->integer('popularity_score')->default(0)->after('benefits');
            $table->integer('min_advance_booking_hours')->default(2)->after('popularity_score');
            $table->integer('max_advance_booking_days')->default(30)->after('min_advance_booking_hours');
            $table->json('available_days')->nullable()->after('max_advance_booking_days'); // Days of week
            $table->json('available_time_slots')->nullable()->after('available_days'); // Time slots
            $table->softDeletes()->after('updated_at');
            
            // Add indexes for better performance
            $table->index('is_featured');
            $table->index('popularity_score');
        });
    }

    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropSoftDeletes();
            $table->dropIndex(['is_featured']);
            $table->dropIndex(['popularity_score']);
            $table->dropColumn([
                'short_description',
                'is_featured',
                'image',
                'requirements',
                'benefits',
                'popularity_score',
                'min_advance_booking_hours',
                'max_advance_booking_days',
                'available_days',
                'available_time_slots',
            ]);
        });
    }
};
```

Run the migration:

```bash
php artisan migrate
```

## 🛠 Step 3: Creating the Service Controller

Let's create a comprehensive service controller:

```bash
# Create service controller
php artisan make:controller ServiceController --resource
```

Edit `app/Http/Controllers/ServiceController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\ServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class ServiceController extends Controller
{
    public function index(Request $request)
    {
        $query = Service::with('serviceCategory');

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Category filter
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Featured filter
        if ($request->filled('featured')) {
            $query->featured();
        }

        // Price range filter
        if ($request->filled('min_price') || $request->filled('max_price')) {
            $query->priceRange($request->min_price, $request->max_price);
        }

        // Sort functionality
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        $allowedSorts = ['name', 'price', 'duration_minutes', 'popularity_score', 'created_at'];
        if (in_array($sortField, $allowedSorts)) {
            $query->orderBy($sortField, $sortDirection);
        }

        $services = $query->paginate(15)->withQueryString();
        $categories = ServiceCategory::active()->get();

        return view('services.index', compact('services', 'categories'));
    }

    public function create()
    {
        $categories = ServiceCategory::active()->get();
        return view('services.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'service_category_id' => 'required|exists:service_categories,id',
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'price' => 'required|numeric|min:0|max:9999.99',
            'duration_minutes' => 'required|integer|min:1|max:1440',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'requirements' => 'nullable|array',
            'requirements.*' => 'string|max:255',
            'benefits' => 'nullable|array',
            'benefits.*' => 'string|max:255',
            'min_advance_booking_hours' => 'required|integer|min:0|max:168',
            'max_advance_booking_days' => 'required|integer|min:1|max:365',
            'available_days' => 'nullable|array',
            'available_days.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'available_time_slots' => 'nullable|array',
            'available_time_slots.*' => 'string',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('service-images', 'public');
        }

        // Set default values
        $validated['is_active'] = $request->has('is_active');
        $validated['is_featured'] = $request->has('is_featured');

        $service = Service::create($validated);

        return redirect()->route('services.show', $service)
            ->with('success', 'Service created successfully!');
    }

    public function show(Service $service)
    {
        $service->load('serviceCategory');
        return view('services.show', compact('service'));
    }

    public function edit(Service $service)
    {
        $categories = ServiceCategory::active()->get();
        return view('services.edit', compact('service', 'categories'));
    }

    public function update(Request $request, Service $service)
    {
        $validated = $request->validate([
            'service_category_id' => 'required|exists:service_categories,id',
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'price' => 'required|numeric|min:0|max:9999.99',
            'duration_minutes' => 'required|integer|min:1|max:1440',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'requirements' => 'nullable|array',
            'requirements.*' => 'string|max:255',
            'benefits' => 'nullable|array',
            'benefits.*' => 'string|max:255',
            'min_advance_booking_hours' => 'required|integer|min:0|max:168',
            'max_advance_booking_days' => 'required|integer|min:1|max:365',
            'available_days' => 'nullable|array',
            'available_days.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'available_time_slots' => 'nullable|array',
            'available_time_slots.*' => 'string',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($service->image) {
                Storage::disk('public')->delete($service->image);
            }
            $validated['image'] = $request->file('image')->store('service-images', 'public');
        }

        // Set default values
        $validated['is_active'] = $request->has('is_active');
        $validated['is_featured'] = $request->has('is_featured');

        $service->update($validated);

        return redirect()->route('services.show', $service)
            ->with('success', 'Service updated successfully!');
    }

    public function destroy(Service $service)
    {
        // Soft delete the service
        $service->delete();

        return redirect()->route('services.index')
            ->with('success', 'Service deleted successfully!');
    }
}
```

## 🛠 Step 4: Adding Service Routes

Add the service routes to `routes/web.php`:

```php
// Service management routes - accessible by staff and admin
Route::middleware(['auth', 'role:staff,admin'])->group(function () {
    Route::resource('services', ServiceController::class);
});

// Public service catalog - accessible by all authenticated users
Route::middleware(['auth'])->group(function () {
    Route::get('/catalog', [ServiceController::class, 'catalog'])->name('services.catalog');
});
```

## 🧪 Testing the Service Management System

1. **Test Service Creation**:
   - Visit `/services/create`
   - Create services with different categories
   - Upload service images
   - Set availability and pricing

2. **Test Service Listing**:
   - Visit `/services`
   - Test search and filtering
   - Verify sorting functionality

3. **Test Service Updates**:
   - Edit existing services
   - Update pricing and availability
   - Change service status

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Enhanced the Service model with advanced features
✅ Added service image uploads and management
✅ Created comprehensive service CRUD operations
✅ Built a professional service management interface
✅ Implemented search and filtering functionality
✅ Added service availability and scheduling features
✅ Created service pricing and duration management

### Service Features Implemented:
- **Service Categories**: Organized service structure
- **Advanced Pricing**: Flexible pricing with duration
- **Image Management**: Service photos with upload
- **Availability Control**: Day and time slot management
- **Popularity Tracking**: Service usage analytics
- **Search & Filter**: Easy service discovery
- **Status Management**: Active/inactive service control

## 🚀 What's Next?

In the next chapter, we'll:
- Create the booking system using our services
- Implement date and time selection
- Build booking validation and conflict detection
- Add booking status management
- Create booking confirmation system

---

**Ready to handle bookings?** Let's move on to [Chapter 6: Basic Booking System](./06-basic-booking-system.md)!
