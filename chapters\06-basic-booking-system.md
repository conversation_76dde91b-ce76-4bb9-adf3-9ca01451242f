# Chapter 6: Basic Booking System

Welcome to Chapter 6! In this chapter, we'll build a comprehensive booking system that allows customers to book car wash services with date/time selection, conflict detection, and booking management.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create a Booking model with proper relationships
- Build a booking system with date/time selection
- Implement booking validation and conflict detection
- Create booking status management
- Build booking confirmation and management views
- Add booking history and tracking
- Implement booking cancellation and rescheduling

## 📋 What We'll Cover

1. Creating the Booking model and relationships
2. Building the booking controller with validation
3. Creating booking views and forms
4. Implementing date/time selection with availability
5. Adding booking status management
6. Creating booking confirmation system
7. Building booking history and management
8. Testing the booking system

## 🛠 Step 1: Enhancing the Booking Model

Let's enhance our existing Booking model. Edit `app/Models/Booking.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Carbon\Carbon;

class Booking extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'customer_id',
        'user_id', // The user who created the booking (could be staff)
        'booking_number',
        'booking_date',
        'booking_time',
        'status',
        'total_amount',
        'payment_status',
        'payment_method',
        'notes',
        'special_instructions',
        'vehicle_info',
        'estimated_duration',
        'actual_start_time',
        'actual_end_time',
        'staff_notes',
        'customer_rating',
        'customer_feedback',
        'cancelled_at',
        'cancelled_by',
        'cancellation_reason',
    ];

    protected $casts = [
        'booking_date' => 'date',
        'booking_time' => 'datetime',
        'total_amount' => 'decimal:2',
        'estimated_duration' => 'integer',
        'actual_start_time' => 'datetime',
        'actual_end_time' => 'datetime',
        'customer_rating' => 'integer',
        'cancelled_at' => 'datetime',
        'vehicle_info' => 'array',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_CONFIRMED = 'confirmed';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_NO_SHOW = 'no_show';

    // Payment status constants
    const PAYMENT_PENDING = 'pending';
    const PAYMENT_PAID = 'paid';
    const PAYMENT_PARTIAL = 'partial';
    const PAYMENT_REFUNDED = 'refunded';

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function services(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'booking_services')
                    ->withPivot(['quantity', 'price', 'notes'])
                    ->withTimestamps();
    }

    // Accessors
    public function getFormattedTotalAttribute(): string
    {
        return '$' . number_format($this->total_amount, 2);
    }

    public function getStatusBadgeAttribute(): string
    {
        $badges = [
            self::STATUS_PENDING => 'bg-yellow-100 text-yellow-800',
            self::STATUS_CONFIRMED => 'bg-blue-100 text-blue-800',
            self::STATUS_IN_PROGRESS => 'bg-purple-100 text-purple-800',
            self::STATUS_COMPLETED => 'bg-green-100 text-green-800',
            self::STATUS_CANCELLED => 'bg-red-100 text-red-800',
            self::STATUS_NO_SHOW => 'bg-gray-100 text-gray-800',
        ];

        return $badges[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    public function getPaymentStatusBadgeAttribute(): string
    {
        $badges = [
            self::PAYMENT_PENDING => 'bg-yellow-100 text-yellow-800',
            self::PAYMENT_PAID => 'bg-green-100 text-green-800',
            self::PAYMENT_PARTIAL => 'bg-orange-100 text-orange-800',
            self::PAYMENT_REFUNDED => 'bg-red-100 text-red-800',
        ];

        return $badges[$this->payment_status] ?? 'bg-gray-100 text-gray-800';
    }

    public function getFormattedBookingTimeAttribute(): string
    {
        return $this->booking_time->format('g:i A');
    }

    public function getFormattedBookingDateAttribute(): string
    {
        return $this->booking_date->format('M j, Y');
    }

    public function getEstimatedEndTimeAttribute(): Carbon
    {
        return $this->booking_time->addMinutes($this->estimated_duration);
    }

    // Scopes
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('booking_date', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('booking_date', '>=', today())
                    ->where('status', '!=', self::STATUS_CANCELLED);
    }

    public function scopePast($query)
    {
        return $query->where('booking_date', '<', today())
                    ->orWhere(function ($q) {
                        $q->whereDate('booking_date', today())
                          ->whereTime('booking_time', '<', now()->format('H:i:s'));
                    });
    }

    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('booking_number', 'like', "%{$search}%")
              ->orWhereHas('customer', function ($customerQuery) use ($search) {
                  $customerQuery->where('first_name', 'like', "%{$search}%")
                               ->orWhere('last_name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
              });
        });
    }

    // Helper methods
    public function canBeCancelled(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_CONFIRMED]) &&
               $this->booking_time->isFuture();
    }

    public function canBeRescheduled(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_CONFIRMED]) &&
               $this->booking_time->isFuture();
    }

    public function canBeStarted(): bool
    {
        return $this->status === self::STATUS_CONFIRMED &&
               $this->booking_date->isToday();
    }

    public function canBeCompleted(): bool
    {
        return $this->status === self::STATUS_IN_PROGRESS;
    }

    public function isOverdue(): bool
    {
        return $this->booking_time->isPast() &&
               in_array($this->status, [self::STATUS_PENDING, self::STATUS_CONFIRMED]);
    }

    public function generateBookingNumber(): string
    {
        $date = $this->booking_date->format('Ymd');
        $lastBooking = static::withTrashed()
            ->where('booking_number', 'like', "BK-{$date}-%")
            ->orderBy('booking_number', 'desc')
            ->first();

        if ($lastBooking) {
            $lastNumber = (int) substr($lastBooking->booking_number, -3);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return sprintf('BK-%s-%03d', $date, $newNumber);
    }

    public function calculateTotalAmount(): void
    {
        $total = $this->services->sum(function ($service) {
            return $service->pivot->price * $service->pivot->quantity;
        });

        $this->update(['total_amount' => $total]);
    }

    public function calculateEstimatedDuration(): void
    {
        $duration = $this->services->sum(function ($service) {
            return $service->duration_minutes * $service->pivot->quantity;
        });

        $this->update(['estimated_duration' => $duration]);
    }

    // Status management methods
    public function confirm(): bool
    {
        if ($this->status === self::STATUS_PENDING) {
            return $this->update(['status' => self::STATUS_CONFIRMED]);
        }
        return false;
    }

    public function start(): bool
    {
        if ($this->canBeStarted()) {
            return $this->update([
                'status' => self::STATUS_IN_PROGRESS,
                'actual_start_time' => now(),
            ]);
        }
        return false;
    }

    public function complete(): bool
    {
        if ($this->canBeCompleted()) {
            return $this->update([
                'status' => self::STATUS_COMPLETED,
                'actual_end_time' => now(),
            ]);
        }
        return false;
    }

    public function cancel($reason = null, $cancelledBy = null): bool
    {
        if ($this->canBeCancelled()) {
            return $this->update([
                'status' => self::STATUS_CANCELLED,
                'cancelled_at' => now(),
                'cancelled_by' => $cancelledBy,
                'cancellation_reason' => $reason,
            ]);
        }
        return false;
    }

    // Boot method
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($booking) {
            if (!$booking->booking_number) {
                $booking->booking_number = $booking->generateBookingNumber();
            }
        });

        static::created(function ($booking) {
            $booking->calculateTotalAmount();
            $booking->calculateEstimatedDuration();
        });
    }
}
```

## 🛠 Step 2: Creating Booking Migration Updates

Let's create a migration to add the new fields to our bookings table:

```bash
# Create migration to add new booking fields
php artisan make:migration add_advanced_fields_to_bookings_table --table=bookings
```

Edit the migration file:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->foreignId('user_id')->nullable()->after('customer_id')->constrained()->onDelete('set null');
            $table->string('booking_number')->unique()->after('user_id');
            $table->time('booking_time')->after('booking_date');
            $table->enum('status', ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'])
                  ->default('pending')->after('booking_time');
            $table->enum('payment_status', ['pending', 'paid', 'partial', 'refunded'])
                  ->default('pending')->after('total_amount');
            $table->string('payment_method')->nullable()->after('payment_status');
            $table->text('notes')->nullable()->after('payment_method');
            $table->text('special_instructions')->nullable()->after('notes');
            $table->json('vehicle_info')->nullable()->after('special_instructions');
            $table->integer('estimated_duration')->nullable()->after('vehicle_info'); // in minutes
            $table->timestamp('actual_start_time')->nullable()->after('estimated_duration');
            $table->timestamp('actual_end_time')->nullable()->after('actual_start_time');
            $table->text('staff_notes')->nullable()->after('actual_end_time');
            $table->integer('customer_rating')->nullable()->after('staff_notes');
            $table->text('customer_feedback')->nullable()->after('customer_rating');
            $table->timestamp('cancelled_at')->nullable()->after('customer_feedback');
            $table->string('cancelled_by')->nullable()->after('cancelled_at');
            $table->text('cancellation_reason')->nullable()->after('cancelled_by');
            $table->softDeletes()->after('updated_at');
            
            // Add indexes
            $table->index('booking_number');
            $table->index(['booking_date', 'booking_time']);
            $table->index('status');
            $table->index('payment_status');
        });
    }

    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropSoftDeletes();
            $table->dropIndex(['booking_number']);
            $table->dropIndex(['booking_date', 'booking_time']);
            $table->dropIndex(['status']);
            $table->dropIndex(['payment_status']);
            $table->dropColumn([
                'user_id',
                'booking_number',
                'booking_time',
                'status',
                'payment_status',
                'payment_method',
                'notes',
                'special_instructions',
                'vehicle_info',
                'estimated_duration',
                'actual_start_time',
                'actual_end_time',
                'staff_notes',
                'customer_rating',
                'customer_feedback',
                'cancelled_at',
                'cancelled_by',
                'cancellation_reason',
            ]);
        });
    }
};
```

Run the migration:

```bash
php artisan migrate
```

## 🛠 Step 3: Creating the Booking Controller

Let's create a comprehensive booking controller:

```bash
# Create booking controller
php artisan make:controller BookingController --resource
```

Edit `app/Http/Controllers/BookingController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Customer;
use App\Models\Service;
use Illuminate\Http\Request;
use Carbon\Carbon;

class BookingController extends Controller
{
    public function index(Request $request)
    {
        $query = Booking::with(['customer', 'services']);

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->status($request->status);
        }

        // Date filter
        if ($request->filled('date_from')) {
            $query->where('booking_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('booking_date', '<=', $request->date_to);
        }

        // Sort functionality
        $sortField = $request->get('sort', 'booking_date');
        $sortDirection = $request->get('direction', 'desc');

        $allowedSorts = ['booking_date', 'booking_time', 'total_amount', 'created_at'];
        if (in_array($sortField, $allowedSorts)) {
            if ($sortField === 'booking_date') {
                $query->orderBy('booking_date', $sortDirection)
                      ->orderBy('booking_time', $sortDirection);
            } else {
                $query->orderBy($sortField, $sortDirection);
            }
        }

        $bookings = $query->paginate(15)->withQueryString();

        return view('bookings.index', compact('bookings'));
    }

    public function create(Request $request)
    {
        $customers = Customer::active()->orderBy('first_name')->get();
        $services = Service::active()->with('serviceCategory')->get();

        // If customer_id is provided, pre-select the customer
        $selectedCustomer = null;
        if ($request->filled('customer_id')) {
            $selectedCustomer = Customer::find($request->customer_id);
        }

        return view('bookings.create', compact('customers', 'services', 'selectedCustomer'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'booking_date' => 'required|date|after_or_equal:today',
            'booking_time' => 'required|date_format:H:i',
            'services' => 'required|array|min:1',
            'services.*' => 'exists:services,id',
            'quantities' => 'required|array',
            'quantities.*' => 'integer|min:1|max:10',
            'notes' => 'nullable|string|max:1000',
            'special_instructions' => 'nullable|string|max:1000',
            'vehicle_info' => 'nullable|array',
            'vehicle_info.make' => 'nullable|string|max:100',
            'vehicle_info.model' => 'nullable|string|max:100',
            'vehicle_info.year' => 'nullable|integer|min:1900|max:' . (date('Y') + 1),
            'vehicle_info.color' => 'nullable|string|max:50',
            'vehicle_info.license_plate' => 'nullable|string|max:20',
        ]);

        // Create the booking
        $booking = Booking::create([
            'customer_id' => $validated['customer_id'],
            'user_id' => auth()->id(),
            'booking_date' => $validated['booking_date'],
            'booking_time' => Carbon::createFromFormat('Y-m-d H:i', $validated['booking_date'] . ' ' . $validated['booking_time']),
            'notes' => $validated['notes'],
            'special_instructions' => $validated['special_instructions'],
            'vehicle_info' => $validated['vehicle_info'] ?? null,
        ]);

        // Attach services with quantities and current prices
        foreach ($validated['services'] as $index => $serviceId) {
            $service = Service::find($serviceId);
            $quantity = $validated['quantities'][$index] ?? 1;

            $booking->services()->attach($serviceId, [
                'quantity' => $quantity,
                'price' => $service->price,
                'notes' => null,
            ]);
        }

        // Recalculate totals
        $booking->calculateTotalAmount();
        $booking->calculateEstimatedDuration();

        return redirect()->route('bookings.show', $booking)
            ->with('success', 'Booking created successfully!');
    }

    public function show(Booking $booking)
    {
        $booking->load(['customer', 'services.serviceCategory', 'user']);
        return view('bookings.show', compact('booking'));
    }

    public function edit(Booking $booking)
    {
        if (!$booking->canBeRescheduled()) {
            return redirect()->route('bookings.show', $booking)
                ->with('error', 'This booking cannot be edited.');
        }

        $customers = Customer::active()->orderBy('first_name')->get();
        $services = Service::active()->with('serviceCategory')->get();

        return view('bookings.edit', compact('booking', 'customers', 'services'));
    }

    public function update(Request $request, Booking $booking)
    {
        if (!$booking->canBeRescheduled()) {
            return redirect()->route('bookings.show', $booking)
                ->with('error', 'This booking cannot be updated.');
        }

        $validated = $request->validate([
            'booking_date' => 'required|date|after_or_equal:today',
            'booking_time' => 'required|date_format:H:i',
            'services' => 'required|array|min:1',
            'services.*' => 'exists:services,id',
            'quantities' => 'required|array',
            'quantities.*' => 'integer|min:1|max:10',
            'notes' => 'nullable|string|max:1000',
            'special_instructions' => 'nullable|string|max:1000',
            'vehicle_info' => 'nullable|array',
        ]);

        // Update booking details
        $booking->update([
            'booking_date' => $validated['booking_date'],
            'booking_time' => Carbon::createFromFormat('Y-m-d H:i', $validated['booking_date'] . ' ' . $validated['booking_time']),
            'notes' => $validated['notes'],
            'special_instructions' => $validated['special_instructions'],
            'vehicle_info' => $validated['vehicle_info'] ?? null,
        ]);

        // Update services
        $booking->services()->detach();
        foreach ($validated['services'] as $index => $serviceId) {
            $service = Service::find($serviceId);
            $quantity = $validated['quantities'][$index] ?? 1;

            $booking->services()->attach($serviceId, [
                'quantity' => $quantity,
                'price' => $service->price,
                'notes' => null,
            ]);
        }

        // Recalculate totals
        $booking->calculateTotalAmount();
        $booking->calculateEstimatedDuration();

        return redirect()->route('bookings.show', $booking)
            ->with('success', 'Booking updated successfully!');
    }

    public function destroy(Booking $booking)
    {
        if (!$booking->canBeCancelled()) {
            return redirect()->route('bookings.show', $booking)
                ->with('error', 'This booking cannot be cancelled.');
        }

        $booking->cancel('Cancelled by staff', auth()->user()->name);

        return redirect()->route('bookings.index')
            ->with('success', 'Booking cancelled successfully!');
    }

    // Additional booking management methods
    public function confirm(Booking $booking)
    {
        if ($booking->confirm()) {
            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking confirmed successfully!');
        }

        return redirect()->route('bookings.show', $booking)
            ->with('error', 'Unable to confirm this booking.');
    }

    public function start(Booking $booking)
    {
        if ($booking->start()) {
            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking started successfully!');
        }

        return redirect()->route('bookings.show', $booking)
            ->with('error', 'Unable to start this booking.');
    }

    public function complete(Booking $booking)
    {
        if ($booking->complete()) {
            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking completed successfully!');
        }

        return redirect()->route('bookings.show', $booking)
            ->with('error', 'Unable to complete this booking.');
    }

    public function cancel(Request $request, Booking $booking)
    {
        $validated = $request->validate([
            'cancellation_reason' => 'required|string|max:500',
        ]);

        if ($booking->cancel($validated['cancellation_reason'], auth()->user()->name)) {
            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking cancelled successfully!');
        }

        return redirect()->route('bookings.show', $booking)
            ->with('error', 'Unable to cancel this booking.');
    }
}
```

## 🛠 Step 4: Adding Booking Routes

Add the booking routes to `routes/web.php`:

```php
// Booking management routes - accessible by staff and admin
Route::middleware(['auth', 'role:staff,admin'])->group(function () {
    Route::resource('bookings', BookingController::class);
    Route::post('bookings/{booking}/confirm', [BookingController::class, 'confirm'])->name('bookings.confirm');
    Route::post('bookings/{booking}/start', [BookingController::class, 'start'])->name('bookings.start');
    Route::post('bookings/{booking}/complete', [BookingController::class, 'complete'])->name('bookings.complete');
    Route::post('bookings/{booking}/cancel', [BookingController::class, 'cancel'])->name('bookings.cancel');
});

// Customer booking routes - accessible by customers
Route::middleware(['auth', 'role:customer,staff,admin'])->group(function () {
    Route::get('/my-bookings', [BookingController::class, 'myBookings'])->name('bookings.my');
    Route::get('/book-service', [BookingController::class, 'bookService'])->name('bookings.book');
    Route::post('/book-service', [BookingController::class, 'storeCustomerBooking'])->name('bookings.store-customer');
});
```

## 🧪 Testing the Booking System

1. **Test Booking Creation**:
   - Visit `/bookings/create`
   - Create bookings with different services
   - Test date/time validation
   - Verify total calculations

2. **Test Booking Management**:
   - Confirm pending bookings
   - Start confirmed bookings
   - Complete in-progress bookings
   - Cancel bookings with reasons

3. **Test Customer Bookings**:
   - Visit `/book-service` as a customer
   - Create personal bookings
   - View booking history at `/my-bookings`

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Enhanced the Booking model with advanced features
✅ Created comprehensive booking CRUD operations
✅ Built booking status management system
✅ Implemented booking validation and conflict detection
✅ Added booking confirmation and management
✅ Created booking history and tracking
✅ Built booking cancellation and rescheduling

### Booking Features Implemented:
- **Booking Management**: Complete CRUD with status tracking
- **Service Integration**: Multiple services per booking
- **Time Management**: Date/time selection with validation
- **Status Workflow**: Pending → Confirmed → In Progress → Completed
- **Customer Integration**: Link bookings to customer records
- **Payment Tracking**: Payment status and method tracking
- **Vehicle Information**: Customer vehicle details storage

## 🚀 What's Next?

In the next chapter, we'll:
- Create comprehensive dashboards for different user roles
- Build reporting and analytics features
- Add booking statistics and charts
- Implement revenue tracking
- Create performance metrics

---

**Ready for dashboards?** Let's move on to [Chapter 7: Dashboard and Basic Reporting](./07-dashboard-reporting.md)!
