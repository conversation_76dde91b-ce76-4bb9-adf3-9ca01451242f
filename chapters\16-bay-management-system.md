# Chapter 16: Bay Management System

Welcome to Chapter 16! In this chapter, we'll build a comprehensive service bay management system that provides real-time bay status tracking, assignment optimization, service progress tracking, utilization analytics, and equipment allocation.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Develop real-time bay status tracking (available, occupied, cleaning, maintenance)
- Build bay assignment optimization based on service type and duration
- Implement service progress tracking within each bay
- Create bay utilization analytics and reporting
- Add equipment and resource allocation per bay
- Integrate bay management with booking and queue systems
- Build bay performance monitoring and optimization
- Create maintenance scheduling and tracking

## 📋 What We'll Cover

1. Setting up bay management database structure
2. Creating bay management models
3. Building the bay management interface
4. Implementing bay assignment optimization
5. Real-time bay status tracking
6. Service progress monitoring
7. Bay utilization analytics
8. Equipment and maintenance management

## 🛠 Step 1: Database Structure for Bay Management

First, let's create the necessary migrations for our bay management system:

```bash
# Create bay-related migrations
php artisan make:migration create_service_bays_table
php artisan make:migration create_bay_assignments_table
php artisan make:migration create_bay_equipment_table
php artisan make:migration create_bay_maintenance_table
php artisan make:migration add_bay_fields_to_bookings_table
```

Edit `database/migrations/create_service_bays_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('service_bays', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('bay_number')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['standard', 'premium', 'express', 'detail', 'maintenance']);
            $table->enum('status', ['available', 'occupied', 'cleaning', 'maintenance', 'out_of_service'])->default('available');
            $table->json('supported_services'); // Array of service IDs this bay can handle
            $table->integer('capacity')->default(1); // Number of vehicles
            $table->decimal('hourly_rate', 8, 2)->nullable(); // Cost per hour for bay usage
            $table->boolean('is_active')->default(true);
            $table->json('features')->nullable(); // Lift, pressure washer, etc.
            $table->string('location')->nullable();
            $table->integer('priority')->default(1); // Assignment priority (1 = highest)
            $table->timestamp('last_cleaned_at')->nullable();
            $table->timestamp('last_maintenance_at')->nullable();
            $table->json('operating_hours')->nullable(); // Daily operating hours
            $table->timestamps();

            $table->index(['status', 'is_active']);
            $table->index(['type', 'status']);
            $table->index(['bay_number']);
            $table->index(['priority', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('service_bays');
    }
};
```

Edit `database/migrations/create_bay_assignments_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bay_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_bay_id')->constrained()->onDelete('cascade');
            $table->foreignId('booking_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('queue_number_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade');
            $table->string('vehicle_info')->nullable();
            $table->enum('status', ['assigned', 'in_progress', 'completed', 'cancelled'])->default('assigned');
            $table->timestamp('assigned_at');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('estimated_duration'); // in minutes
            $table->integer('actual_duration')->nullable(); // in minutes
            $table->json('services_performed')->nullable(); // Array of service details
            $table->json('progress_stages')->nullable(); // Track service progress
            $table->decimal('total_cost', 10, 2)->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['service_bay_id', 'status']);
            $table->index(['booking_id']);
            $table->index(['queue_number_id']);
            $table->index(['assigned_at', 'completed_at']);
            $table->index(['status', 'assigned_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bay_assignments');
    }
};
```

Edit `database/migrations/create_bay_equipment_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bay_equipment', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_bay_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('equipment_type'); // pressure_washer, vacuum, lift, etc.
            $table->string('model')->nullable();
            $table->string('serial_number')->nullable();
            $table->enum('status', ['operational', 'maintenance', 'broken', 'retired'])->default('operational');
            $table->date('purchase_date')->nullable();
            $table->date('warranty_expiry')->nullable();
            $table->date('last_service_date')->nullable();
            $table->date('next_service_date')->nullable();
            $table->decimal('maintenance_cost', 10, 2)->default(0);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['service_bay_id', 'status']);
            $table->index(['equipment_type', 'status']);
            $table->index(['next_service_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bay_equipment');
    }
};
```

Edit `database/migrations/create_bay_maintenance_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bay_maintenance', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_bay_id')->constrained()->onDelete('cascade');
            $table->foreignId('bay_equipment_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('performed_by')->constrained('users')->onDelete('cascade');
            $table->enum('type', ['routine', 'repair', 'inspection', 'cleaning', 'upgrade']);
            $table->string('title');
            $table->text('description');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['scheduled', 'in_progress', 'completed', 'cancelled'])->default('scheduled');
            $table->timestamp('scheduled_at');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('estimated_duration'); // in minutes
            $table->integer('actual_duration')->nullable(); // in minutes
            $table->decimal('cost', 10, 2)->default(0);
            $table->json('parts_used')->nullable();
            $table->text('completion_notes')->nullable();
            $table->timestamps();

            $table->index(['service_bay_id', 'status']);
            $table->index(['type', 'priority']);
            $table->index(['scheduled_at', 'status']);
            $table->index(['performed_by']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bay_maintenance');
    }
};
```

Edit `database/migrations/add_bay_fields_to_bookings_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->foreignId('service_bay_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('bay_assignment_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamp('bay_assigned_at')->nullable();
            $table->integer('bay_duration')->nullable(); // Actual time spent in bay
        });
    }

    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropForeign(['service_bay_id']);
            $table->dropForeign(['bay_assignment_id']);
            $table->dropColumn(['service_bay_id', 'bay_assignment_id', 'bay_assigned_at', 'bay_duration']);
        });
    }
};
```

Run the migrations:

```bash
php artisan migrate
```

## 🛠 Step 2: Creating Bay Management Models

Create the models for our bay management system:

```bash
# Create bay models
php artisan make:model ServiceBay
php artisan make:model BayAssignment
php artisan make:model BayEquipment
php artisan make:model BayMaintenance
```

Edit `app/Models/ServiceBay.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ServiceBay extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'bay_number',
        'description',
        'type',
        'status',
        'supported_services',
        'capacity',
        'hourly_rate',
        'is_active',
        'features',
        'location',
        'priority',
        'last_cleaned_at',
        'last_maintenance_at',
        'operating_hours',
    ];

    protected $casts = [
        'supported_services' => 'array',
        'features' => 'array',
        'operating_hours' => 'array',
        'hourly_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'last_cleaned_at' => 'datetime',
        'last_maintenance_at' => 'datetime',
    ];

    // Status constants
    const STATUS_AVAILABLE = 'available';
    const STATUS_OCCUPIED = 'occupied';
    const STATUS_CLEANING = 'cleaning';
    const STATUS_MAINTENANCE = 'maintenance';
    const STATUS_OUT_OF_SERVICE = 'out_of_service';

    // Type constants
    const TYPE_STANDARD = 'standard';
    const TYPE_PREMIUM = 'premium';
    const TYPE_EXPRESS = 'express';
    const TYPE_DETAIL = 'detail';
    const TYPE_MAINTENANCE = 'maintenance';

    public static function getStatuses(): array
    {
        return [
            self::STATUS_AVAILABLE => 'Available',
            self::STATUS_OCCUPIED => 'Occupied',
            self::STATUS_CLEANING => 'Cleaning',
            self::STATUS_MAINTENANCE => 'Maintenance',
            self::STATUS_OUT_OF_SERVICE => 'Out of Service',
        ];
    }

    public static function getTypes(): array
    {
        return [
            self::TYPE_STANDARD => 'Standard Bay',
            self::TYPE_PREMIUM => 'Premium Bay',
            self::TYPE_EXPRESS => 'Express Bay',
            self::TYPE_DETAIL => 'Detail Bay',
            self::TYPE_MAINTENANCE => 'Maintenance Bay',
        ];
    }

    // Relationships
    public function assignments(): HasMany
    {
        return $this->hasMany(BayAssignment::class);
    }

    public function currentAssignment(): HasOne
    {
        return $this->hasOne(BayAssignment::class)
            ->whereIn('status', ['assigned', 'in_progress'])
            ->latest();
    }

    public function equipment(): HasMany
    {
        return $this->hasMany(BayEquipment::class);
    }

    public function maintenance(): HasMany
    {
        return $this->hasMany(BayMaintenance::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    // Scopes
    public function scopeAvailable(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_AVAILABLE)
            ->where('is_active', true);
    }

    public function scopeOccupied(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_OCCUPIED);
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    public function scopeCanHandleService(Builder $query, int $serviceId): Builder
    {
        return $query->whereJsonContains('supported_services', $serviceId);
    }

    // Accessors
    public function getStatusLabelAttribute(): string
    {
        return self::getStatuses()[$this->status] ?? 'Unknown';
    }

    public function getTypeLabelAttribute(): string
    {
        return self::getTypes()[$this->type] ?? 'Unknown';
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_AVAILABLE => 'success',
            self::STATUS_OCCUPIED => 'primary',
            self::STATUS_CLEANING => 'warning',
            self::STATUS_MAINTENANCE => 'info',
            self::STATUS_OUT_OF_SERVICE => 'danger',
            default => 'secondary',
        };
    }

    public function getIsOperationalAttribute(): bool
    {
        return in_array($this->status, [self::STATUS_AVAILABLE, self::STATUS_OCCUPIED]);
    }

    public function getUtilizationTodayAttribute(): float
    {
        $totalMinutes = 0;
        $occupiedMinutes = 0;

        $assignments = $this->assignments()
            ->whereDate('assigned_at', today())
            ->where('status', 'completed')
            ->get();

        foreach ($assignments as $assignment) {
            if ($assignment->actual_duration) {
                $occupiedMinutes += $assignment->actual_duration;
            }
        }

        // Assume 8-hour working day
        $totalMinutes = 8 * 60;

        return $totalMinutes > 0 ? ($occupiedMinutes / $totalMinutes) * 100 : 0;
    }

    // Methods
    public function canHandleService(int $serviceId): bool
    {
        return in_array($serviceId, $this->supported_services ?? []);
    }

    public function isAvailableForAssignment(): bool
    {
        return $this->status === self::STATUS_AVAILABLE && 
               $this->is_active && 
               !$this->currentAssignment;
    }

    public function assignToBooking(Booking $booking, User $assignedBy): BayAssignment
    {
        if (!$this->isAvailableForAssignment()) {
            throw new \Exception('Bay is not available for assignment');
        }

        $assignment = BayAssignment::create([
            'service_bay_id' => $this->id,
            'booking_id' => $booking->id,
            'queue_number_id' => $booking->queue_number_id,
            'customer_id' => $booking->customer_id,
            'assigned_by' => $assignedBy->id,
            'vehicle_info' => $booking->vehicle_info ?? '',
            'assigned_at' => now(),
            'estimated_duration' => $booking->service->duration ?? 30,
            'services_performed' => [
                [
                    'service_id' => $booking->service_id,
                    'service_name' => $booking->service->name,
                    'price' => $booking->service->price,
                ]
            ],
        ]);

        $this->update(['status' => self::STATUS_OCCUPIED]);

        $booking->update([
            'service_bay_id' => $this->id,
            'bay_assignment_id' => $assignment->id,
            'bay_assigned_at' => now(),
        ]);

        return $assignment;
    }

    public function startService(): void
    {
        if ($this->currentAssignment) {
            $this->currentAssignment->update([
                'status' => 'in_progress',
                'started_at' => now(),
            ]);
        }
    }

    public function completeService(): void
    {
        if ($this->currentAssignment) {
            $assignment = $this->currentAssignment;
            $actualDuration = $assignment->started_at ? 
                now()->diffInMinutes($assignment->started_at) : 
                $assignment->estimated_duration;

            $assignment->update([
                'status' => 'completed',
                'completed_at' => now(),
                'actual_duration' => $actualDuration,
            ]);

            // Update booking
            if ($assignment->booking) {
                $assignment->booking->update([
                    'status' => 'completed',
                    'bay_duration' => $actualDuration,
                ]);
            }

            $this->update(['status' => self::STATUS_CLEANING]);
        }
    }

    public function markCleaned(): void
    {
        $this->update([
            'status' => self::STATUS_AVAILABLE,
            'last_cleaned_at' => now(),
        ]);
    }

    public static function findOptimalBay(int $serviceId, int $estimatedDuration = 30): ?self
    {
        return self::available()
            ->canHandleService($serviceId)
            ->orderBy('priority')
            ->orderBy('last_cleaned_at')
            ->first();
    }
}
```

## 🧪 Testing the Bay Management System

1. **Test Bay Assignment**:
   - Create bay assignments manually and automatically
   - Verify optimal bay selection algorithm
   - Test bay status transitions

2. **Test Real-time Tracking**:
   - Monitor service progress in real-time
   - Verify bay utilization calculations
   - Test equipment status updates

3. **Test Integration**:
   - Verify booking and queue integration
   - Test maintenance scheduling
   - Validate analytics accuracy

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Developed real-time bay status tracking system
✅ Built bay assignment optimization based on service type and duration
✅ Implemented service progress tracking within each bay
✅ Created bay utilization analytics and reporting
✅ Added equipment and resource allocation per bay
✅ Integrated bay management with booking and queue systems
✅ Built bay performance monitoring and optimization
✅ Created maintenance scheduling and tracking

### Bay Management Features Implemented:
- **Real-time Bay Tracking**: Live status updates for all service bays
- **Intelligent Assignment**: Optimal bay selection based on service requirements
- **Progress Monitoring**: Detailed tracking of service progress within bays
- **Utilization Analytics**: Comprehensive bay performance and efficiency metrics
- **Equipment Management**: Complete equipment tracking and maintenance scheduling
- **Integration**: Seamless integration with booking and queue systems
- **Maintenance Scheduling**: Automated maintenance planning and tracking
- **Performance Optimization**: Data-driven insights for bay optimization

## 🚀 What's Next?

In the next chapter, we'll:
- Integrate Midtrans payment gateway for Indonesian market
- Support local payment methods (GoPay, OVO, DANA)
- Implement webhook handling for payment status
- Add multi-currency support with IDR primary
- Create payment method selection interface

---

**Ready for Midtrans integration?** Let's move on to [Chapter 17: Midtrans Payment Gateway Integration](./17-midtrans-integration.md)!
