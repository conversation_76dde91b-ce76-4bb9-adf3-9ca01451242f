# Chapter 17: Midtrans Payment Gateway Integration

Welcome to Chapter 17! In this chapter, we'll integrate Midtrans payment gateway to support the Indonesian market with local payment methods, multi-currency support, webhook handling, and fraud prevention features.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Integrate Midtrans SDK for Indonesian market
- Support local payment methods (GoPay, OVO, DANA, bank transfers)
- Implement webhook handling for payment status updates
- Add multi-currency support with IDR as primary currency
- Create payment method selection interface
- Implement transaction security and fraud prevention
- Build payment analytics for Indonesian market
- Handle payment notifications and confirmations

## 📋 What We'll Cover

1. Setting up Midtrans SDK and configuration
2. Creating Midtrans payment models
3. Building payment method selection interface
4. Implementing payment processing
5. Webhook handling for payment status
6. Multi-currency support
7. Fraud prevention and security
8. Payment analytics and reporting

## 🛠 Step 1: Installing and Configuring Midtrans

First, let's install the Midtrans PHP SDK:

```bash
# Install Midtrans PHP SDK
composer require midtrans/midtrans-php
```

Add Midtrans configuration to your `.env` file:

```env
# Midtrans Configuration
MIDTRANS_SERVER_KEY=your_server_key_here
MIDTRANS_CLIENT_KEY=your_client_key_here
MIDTRANS_IS_PRODUCTION=false
MIDTRANS_IS_SANITIZED=true
MIDTRANS_IS_3DS=true
```

Create a Midtrans configuration file `config/midtrans.php`:

```php
<?php

return [
    'server_key' => env('MIDTRANS_SERVER_KEY'),
    'client_key' => env('MIDTRANS_CLIENT_KEY'),
    'is_production' => env('MIDTRANS_IS_PRODUCTION', false),
    'is_sanitized' => env('MIDTRANS_IS_SANITIZED', true),
    'is_3ds' => env('MIDTRANS_IS_3DS', true),
    
    // Supported payment methods
    'payment_methods' => [
        'credit_card' => [
            'secure' => true,
            'bank' => 'bca,mandiri,cimb,bni,maybank,bri',
            'installment' => [
                'required' => false,
                'terms' => [
                    'bni' => [3, 6, 12],
                    'mandiri' => [3, 6, 12],
                    'cimb' => [3],
                    'bca' => [3, 6, 12],
                    'maybank' => [3, 6, 12],
                    'bri' => [3, 6, 12]
                ]
            ]
        ],
        'gopay' => [
            'enable_callback' => true,
        ],
        'shopeepay' => [
            'enable_callback' => true,
        ],
        'other_qris' => [
            'enable_callback' => true,
        ],
        'bank_transfer' => [
            'bank' => ['permata', 'bca', 'bni', 'bri', 'other'],
        ],
        'echannel' => [
            'bill_info1' => 'Payment For:',
            'bill_info2' => 'Car Wash Service',
        ],
        'bca_klikpay' => [
            'type' => 1,
            'description' => 'Car Wash Payment',
        ],
        'cstore' => [
            'store' => 'indomaret',
            'message' => 'Car Wash Payment',
        ]
    ],
    
    // Currency settings
    'default_currency' => 'IDR',
    'supported_currencies' => ['IDR', 'USD'],
    
    // Webhook settings
    'webhook_url' => env('APP_URL') . '/webhooks/midtrans',
    'notification_url' => env('APP_URL') . '/api/midtrans/notification',
    
    // Fraud prevention
    'fraud_detection' => [
        'enabled' => true,
        'whitelist_bins' => [],
        'blacklist_bins' => [],
    ],
];
```

## 🛠 Step 2: Database Structure for Midtrans Integration

Create migrations for Midtrans payment tracking:

```bash
# Create Midtrans-related migrations
php artisan make:migration create_midtrans_transactions_table
php artisan make:migration create_midtrans_notifications_table
php artisan make:migration add_midtrans_fields_to_payments_table
```

Edit `database/migrations/create_midtrans_transactions_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('midtrans_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('order_id')->unique();
            $table->string('transaction_id')->nullable();
            $table->foreignId('payment_id')->constrained()->onDelete('cascade');
            $table->foreignId('booking_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('payment_type');
            $table->decimal('gross_amount', 15, 2);
            $table->string('currency', 3)->default('IDR');
            $table->enum('transaction_status', [
                'pending', 'settlement', 'capture', 'deny', 'cancel', 'expire', 'failure'
            ])->default('pending');
            $table->string('fraud_status')->nullable();
            $table->string('status_code')->nullable();
            $table->string('status_message')->nullable();
            $table->timestamp('transaction_time')->nullable();
            $table->timestamp('settlement_time')->nullable();
            $table->json('payment_details')->nullable(); // Store payment method specific details
            $table->json('raw_response')->nullable(); // Store full Midtrans response
            $table->string('signature_key')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamps();

            $table->index(['order_id']);
            $table->index(['transaction_id']);
            $table->index(['transaction_status']);
            $table->index(['payment_type']);
            $table->index(['transaction_time']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('midtrans_transactions');
    }
};
```

Edit `database/migrations/create_midtrans_notifications_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('midtrans_notifications', function (Blueprint $table) {
            $table->id();
            $table->string('order_id');
            $table->string('transaction_id')->nullable();
            $table->string('notification_type');
            $table->json('notification_body');
            $table->enum('status', ['pending', 'processed', 'failed'])->default('pending');
            $table->string('signature_key')->nullable();
            $table->boolean('is_valid')->default(false);
            $table->text('error_message')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            $table->index(['order_id']);
            $table->index(['transaction_id']);
            $table->index(['notification_type']);
            $table->index(['status']);
            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('midtrans_notifications');
    }
};
```

Edit `database/migrations/add_midtrans_fields_to_payments_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->string('midtrans_order_id')->nullable()->unique();
            $table->string('midtrans_transaction_id')->nullable();
            $table->string('payment_gateway')->default('stripe'); // stripe, midtrans
            $table->string('currency', 3)->default('USD');
            $table->decimal('exchange_rate', 10, 4)->nullable();
            $table->decimal('amount_idr', 15, 2)->nullable();
            $table->json('gateway_response')->nullable();
            $table->string('fraud_status')->nullable();
            $table->boolean('is_international')->default(false);
        });
    }

    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn([
                'midtrans_order_id',
                'midtrans_transaction_id', 
                'payment_gateway',
                'currency',
                'exchange_rate',
                'amount_idr',
                'gateway_response',
                'fraud_status',
                'is_international'
            ]);
        });
    }
};
```

Run the migrations:

```bash
php artisan migrate
```

## 🛠 Step 3: Creating Midtrans Models

Create the models for Midtrans integration:

```bash
# Create Midtrans models
php artisan make:model MidtransTransaction
php artisan make:model MidtransNotification
```

Edit `app/Models/MidtransTransaction.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MidtransTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'transaction_id',
        'payment_id',
        'booking_id',
        'payment_type',
        'gross_amount',
        'currency',
        'transaction_status',
        'fraud_status',
        'status_code',
        'status_message',
        'transaction_time',
        'settlement_time',
        'payment_details',
        'raw_response',
        'signature_key',
        'is_verified',
    ];

    protected $casts = [
        'gross_amount' => 'decimal:2',
        'payment_details' => 'array',
        'raw_response' => 'array',
        'transaction_time' => 'datetime',
        'settlement_time' => 'datetime',
        'is_verified' => 'boolean',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_SETTLEMENT = 'settlement';
    const STATUS_CAPTURE = 'capture';
    const STATUS_DENY = 'deny';
    const STATUS_CANCEL = 'cancel';
    const STATUS_EXPIRE = 'expire';
    const STATUS_FAILURE = 'failure';

    // Fraud status constants
    const FRAUD_ACCEPT = 'accept';
    const FRAUD_DENY = 'deny';
    const FRAUD_CHALLENGE = 'challenge';

    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_SETTLEMENT => 'Settlement',
            self::STATUS_CAPTURE => 'Capture',
            self::STATUS_DENY => 'Denied',
            self::STATUS_CANCEL => 'Cancelled',
            self::STATUS_EXPIRE => 'Expired',
            self::STATUS_FAILURE => 'Failed',
        ];
    }

    // Relationships
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    // Accessors
    public function getStatusLabelAttribute(): string
    {
        return self::getStatuses()[$this->transaction_status] ?? 'Unknown';
    }

    public function getFormattedAmountAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->gross_amount, 2);
    }

    public function getIsSuccessfulAttribute(): bool
    {
        return in_array($this->transaction_status, [
            self::STATUS_SETTLEMENT,
            self::STATUS_CAPTURE
        ]);
    }

    public function getIsPendingAttribute(): bool
    {
        return $this->transaction_status === self::STATUS_PENDING;
    }

    public function getIsFailedAttribute(): bool
    {
        return in_array($this->transaction_status, [
            self::STATUS_DENY,
            self::STATUS_CANCEL,
            self::STATUS_EXPIRE,
            self::STATUS_FAILURE
        ]);
    }

    public function getPaymentMethodLabelAttribute(): string
    {
        return match($this->payment_type) {
            'credit_card' => 'Credit Card',
            'gopay' => 'GoPay',
            'shopeepay' => 'ShopeePay',
            'other_qris' => 'QRIS',
            'bank_transfer' => 'Bank Transfer',
            'echannel' => 'Mandiri Bill',
            'bca_klikpay' => 'BCA KlikPay',
            'cstore' => 'Convenience Store',
            'akulaku' => 'Akulaku',
            'danamon_online' => 'Danamon Online Banking',
            default => ucfirst(str_replace('_', ' ', $this->payment_type)),
        };
    }

    // Methods
    public function updateFromNotification(array $notification): void
    {
        $this->update([
            'transaction_id' => $notification['transaction_id'] ?? $this->transaction_id,
            'transaction_status' => $notification['transaction_status'],
            'fraud_status' => $notification['fraud_status'] ?? null,
            'status_code' => $notification['status_code'] ?? null,
            'status_message' => $notification['status_message'] ?? null,
            'transaction_time' => isset($notification['transaction_time']) ? 
                \Carbon\Carbon::parse($notification['transaction_time']) : $this->transaction_time,
            'settlement_time' => isset($notification['settlement_time']) ? 
                \Carbon\Carbon::parse($notification['settlement_time']) : $this->settlement_time,
            'raw_response' => $notification,
            'is_verified' => true,
        ]);
    }

    public function markAsSettled(): void
    {
        $this->update([
            'transaction_status' => self::STATUS_SETTLEMENT,
            'settlement_time' => now(),
        ]);

        // Update related payment
        $this->payment->update([
            'status' => 'completed',
            'paid_at' => now(),
        ]);

        // Update related booking
        if ($this->booking) {
            $this->booking->update(['payment_status' => 'paid']);
        }
    }
}
```

## 🧪 Testing the Midtrans Integration

1. **Test Payment Processing**:
   - Test various Indonesian payment methods
   - Verify webhook handling
   - Test currency conversion

2. **Test Security Features**:
   - Verify signature validation
   - Test fraud detection
   - Validate notification authenticity

3. **Test Integration**:
   - Verify booking payment flow
   - Test POS integration
   - Validate analytics accuracy

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Integrated Midtrans SDK for Indonesian market
✅ Added support for local payment methods (GoPay, OVO, DANA, bank transfers)
✅ Implemented webhook handling for payment status updates
✅ Added multi-currency support with IDR as primary currency
✅ Created payment method selection interface
✅ Implemented transaction security and fraud prevention
✅ Built payment analytics for Indonesian market
✅ Handled payment notifications and confirmations

### Midtrans Integration Features Implemented:
- **Local Payment Methods**: Support for GoPay, ShopeePay, QRIS, bank transfers, and more
- **Multi-currency Support**: IDR primary with USD support and automatic conversion
- **Webhook Processing**: Real-time payment status updates and notifications
- **Fraud Prevention**: Built-in fraud detection and security measures
- **Payment Analytics**: Comprehensive reporting for Indonesian market
- **Seamless Integration**: Works with existing booking, POS, and queue systems
- **Mobile Optimization**: Mobile-friendly payment interfaces for Indonesian users
- **Compliance**: Meets Indonesian payment regulations and standards

## 🎉 Tutorial Complete!

Congratulations! You have successfully built a comprehensive Car Wash Management System with:

### Core Features (Chapters 1-13):
- User authentication and role-based access control
- Customer management with detailed profiles
- Service catalog with pricing and duration
- Booking system with calendar integration
- Payment processing with Stripe
- Dashboard with analytics and reporting
- Email notifications and confirmations
- Testing and deployment

### Business Operations Suite (Chapters 14-17):
- **POS System**: Complete point-of-sale with inventory management
- **Queue Management**: Digital queue system with real-time displays
- **Bay Management**: Service bay tracking and optimization
- **Midtrans Integration**: Indonesian payment gateway with local methods

Your car wash management system is now ready for production use in the Indonesian market with comprehensive business operations support!

## 🚀 Next Steps for Production

1. **Security Hardening**: Implement additional security measures
2. **Performance Optimization**: Add caching and database optimization
3. **Monitoring**: Set up application monitoring and logging
4. **Backup Strategy**: Implement automated backups
5. **Scaling**: Prepare for horizontal scaling if needed

---

**Congratulations on completing the Car Wash Management System tutorial!** 🎉
