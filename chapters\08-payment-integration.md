# Chapter 8: Payment Integration

Welcome to Chapter 8! In this chapter, we'll integrate Stripe payment processing to handle service payments, manage payment confirmations, and implement secure payment workflows.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Install and configure Stripe for Laravel
- Create payment models and migrations
- Build secure payment processing workflows
- Handle payment confirmations and webhooks
- Implement payment history and refund management
- Add payment security and fraud protection
- Create payment-related notifications

## 📋 What We'll Cover

1. Installing and configuring Stripe
2. Creating Payment model and relationships
3. Building payment processing controller
4. Implementing secure payment forms
5. Handling Stripe webhooks
6. Adding payment history and management
7. Creating refund functionality
8. Testing the payment system

## 🛠 Step 1: Installing Stripe for Laravel

First, let's install the Stripe PHP SDK:

```bash
# Install Stripe PHP SDK
composer require stripe/stripe-php

# Install Laravel Cashier (optional, for advanced features)
composer require laravel/cashier
```

Add Stripe configuration to your `.env` file:

```env
# Stripe Configuration
STRIPE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

Add the Stripe configuration to `config/services.php`:

```php
// Add to config/services.php
'stripe' => [
    'model' => App\Models\User::class,
    'key' => env('STRIPE_KEY'),
    'secret' => env('STRIPE_SECRET'),
    'webhook' => [
        'secret' => env('STRIPE_WEBHOOK_SECRET'),
        'tolerance' => env('STRIPE_WEBHOOK_TOLERANCE', 300),
    ],
],
```

## 🛠 Step 2: Creating the Payment Model

Let's create a Payment model to track all payment transactions:

```bash
# Create Payment model and migration
php artisan make:model Payment -m
```

Edit the migration file `database/migrations/xxxx_xx_xx_xxxxxx_create_payments_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->string('payment_intent_id')->unique(); // Stripe Payment Intent ID
            $table->string('payment_method_id')->nullable(); // Stripe Payment Method ID
            $table->decimal('amount', 10, 2); // Payment amount
            $table->decimal('fee_amount', 10, 2)->default(0); // Stripe fees
            $table->decimal('net_amount', 10, 2); // Amount after fees
            $table->string('currency', 3)->default('usd');
            $table->enum('status', [
                'pending',
                'processing',
                'succeeded',
                'failed',
                'canceled',
                'refunded',
                'partially_refunded'
            ])->default('pending');
            $table->enum('type', ['payment', 'refund'])->default('payment');
            $table->string('stripe_charge_id')->nullable();
            $table->string('stripe_refund_id')->nullable();
            $table->json('payment_method_details')->nullable(); // Card details, etc.
            $table->json('metadata')->nullable(); // Additional data
            $table->text('failure_reason')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->timestamp('refunded_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('payment_intent_id');
            $table->index('status');
            $table->index('type');
            $table->index(['booking_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
```

Now edit the Payment model `app/Models/Payment.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_id',
        'customer_id',
        'payment_intent_id',
        'payment_method_id',
        'amount',
        'fee_amount',
        'net_amount',
        'currency',
        'status',
        'type',
        'stripe_charge_id',
        'stripe_refund_id',
        'payment_method_details',
        'metadata',
        'failure_reason',
        'paid_at',
        'failed_at',
        'refunded_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'fee_amount' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'payment_method_details' => 'array',
        'metadata' => 'array',
        'paid_at' => 'datetime',
        'failed_at' => 'datetime',
        'refunded_at' => 'datetime',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCEEDED = 'succeeded';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELED = 'canceled';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_PARTIALLY_REFUNDED = 'partially_refunded';

    // Type constants
    const TYPE_PAYMENT = 'payment';
    const TYPE_REFUND = 'refund';

    // Relationships
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Accessors
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    public function getFormattedFeeAmountAttribute(): string
    {
        return '$' . number_format($this->fee_amount, 2);
    }

    public function getFormattedNetAmountAttribute(): string
    {
        return '$' . number_format($this->net_amount, 2);
    }

    public function getStatusBadgeAttribute(): string
    {
        $badges = [
            self::STATUS_PENDING => 'bg-yellow-100 text-yellow-800',
            self::STATUS_PROCESSING => 'bg-blue-100 text-blue-800',
            self::STATUS_SUCCEEDED => 'bg-green-100 text-green-800',
            self::STATUS_FAILED => 'bg-red-100 text-red-800',
            self::STATUS_CANCELED => 'bg-gray-100 text-gray-800',
            self::STATUS_REFUNDED => 'bg-purple-100 text-purple-800',
            self::STATUS_PARTIALLY_REFUNDED => 'bg-orange-100 text-orange-800',
        ];

        return $badges[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    public function getPaymentMethodTypeAttribute(): string
    {
        if (!$this->payment_method_details) {
            return 'Unknown';
        }

        $details = $this->payment_method_details;
        
        if (isset($details['card'])) {
            return 'Card (**** ' . $details['card']['last4'] . ')';
        }

        return ucfirst($details['type'] ?? 'Unknown');
    }

    // Scopes
    public function scopeSuccessful($query)
    {
        return $query->where('status', self::STATUS_SUCCEEDED);
    }

    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeRefunded($query)
    {
        return $query->whereIn('status', [self::STATUS_REFUNDED, self::STATUS_PARTIALLY_REFUNDED]);
    }

    public function scopeForBooking($query, $bookingId)
    {
        return $query->where('booking_id', $bookingId);
    }

    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    // Helper methods
    public function isSuccessful(): bool
    {
        return $this->status === self::STATUS_SUCCEEDED;
    }

    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    public function isRefunded(): bool
    {
        return in_array($this->status, [self::STATUS_REFUNDED, self::STATUS_PARTIALLY_REFUNDED]);
    }

    public function canBeRefunded(): bool
    {
        return $this->isSuccessful() && !$this->isRefunded();
    }

    public function markAsSucceeded($chargeId = null, $paidAt = null): bool
    {
        return $this->update([
            'status' => self::STATUS_SUCCEEDED,
            'stripe_charge_id' => $chargeId,
            'paid_at' => $paidAt ?: now(),
        ]);
    }

    public function markAsFailed($reason = null, $failedAt = null): bool
    {
        return $this->update([
            'status' => self::STATUS_FAILED,
            'failure_reason' => $reason,
            'failed_at' => $failedAt ?: now(),
        ]);
    }

    public function markAsRefunded($refundId = null, $refundedAt = null): bool
    {
        return $this->update([
            'status' => self::STATUS_REFUNDED,
            'stripe_refund_id' => $refundId,
            'refunded_at' => $refundedAt ?: now(),
        ]);
    }

    public function calculateFees(): void
    {
        // Stripe fee calculation (2.9% + $0.30 for US cards)
        $stripeFee = ($this->amount * 0.029) + 0.30;
        $this->update([
            'fee_amount' => round($stripeFee, 2),
            'net_amount' => round($this->amount - $stripeFee, 2),
        ]);
    }
}
```

Run the migration:

```bash
php artisan migrate
```

## 🛠 Step 3: Creating the Payment Controller

Let's create a comprehensive payment controller:

```bash
# Create payment controller
php artisan make:controller PaymentController
```

Edit `app/Http/Controllers/PaymentController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use Illuminate\Http\Request;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    public function createPaymentIntent(Request $request)
    {
        $validated = $request->validate([
            'booking_id' => 'required|exists:bookings,id',
        ]);

        $booking = Booking::with(['customer', 'services'])->findOrFail($validated['booking_id']);

        // Check if booking can be paid
        if ($booking->payment_status === 'paid') {
            return response()->json(['error' => 'Booking is already paid'], 400);
        }

        try {
            // Create or retrieve existing payment intent
            $payment = Payment::where('booking_id', $booking->id)
                             ->where('status', Payment::STATUS_PENDING)
                             ->first();

            if (!$payment) {
                // Create Stripe Payment Intent
                $paymentIntent = PaymentIntent::create([
                    'amount' => $booking->total_amount * 100, // Convert to cents
                    'currency' => 'usd',
                    'metadata' => [
                        'booking_id' => $booking->id,
                        'customer_id' => $booking->customer_id,
                        'booking_number' => $booking->booking_number,
                    ],
                    'automatic_payment_methods' => [
                        'enabled' => true,
                    ],
                ]);

                // Create payment record
                $payment = Payment::create([
                    'booking_id' => $booking->id,
                    'customer_id' => $booking->customer_id,
                    'payment_intent_id' => $paymentIntent->id,
                    'amount' => $booking->total_amount,
                    'currency' => 'usd',
                    'status' => Payment::STATUS_PENDING,
                    'type' => Payment::TYPE_PAYMENT,
                ]);

                $payment->calculateFees();
            } else {
                // Retrieve existing payment intent
                $paymentIntent = PaymentIntent::retrieve($payment->payment_intent_id);
            }

            return response()->json([
                'client_secret' => $paymentIntent->client_secret,
                'payment_id' => $payment->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Payment Intent Creation Failed: ' . $e->getMessage());
            return response()->json(['error' => 'Payment setup failed'], 500);
        }
    }

    public function confirmPayment(Request $request)
    {
        $validated = $request->validate([
            'payment_intent_id' => 'required|string',
        ]);

        try {
            $paymentIntent = PaymentIntent::retrieve($validated['payment_intent_id']);
            $payment = Payment::where('payment_intent_id', $paymentIntent->id)->firstOrFail();

            if ($paymentIntent->status === 'succeeded') {
                $payment->markAsSucceeded($paymentIntent->charges->data[0]->id ?? null);
                
                // Update booking payment status
                $payment->booking->update([
                    'payment_status' => 'paid',
                    'payment_method' => $paymentIntent->payment_method_types[0] ?? 'card',
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Payment confirmed successfully',
                    'payment' => $payment,
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Payment not completed',
            ]);

        } catch (\Exception $e) {
            Log::error('Payment Confirmation Failed: ' . $e->getMessage());
            return response()->json(['error' => 'Payment confirmation failed'], 500);
        }
    }

    public function handleWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook.secret');

        try {
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (\UnexpectedValueException $e) {
            Log::error('Invalid payload in webhook: ' . $e->getMessage());
            return response('Invalid payload', 400);
        } catch (SignatureVerificationException $e) {
            Log::error('Invalid signature in webhook: ' . $e->getMessage());
            return response('Invalid signature', 400);
        }

        // Handle the event
        switch ($event->type) {
            case 'payment_intent.succeeded':
                $this->handlePaymentSucceeded($event->data->object);
                break;
            case 'payment_intent.payment_failed':
                $this->handlePaymentFailed($event->data->object);
                break;
            case 'charge.dispute.created':
                $this->handleChargeDispute($event->data->object);
                break;
            default:
                Log::info('Unhandled webhook event type: ' . $event->type);
        }

        return response('Webhook handled', 200);
    }

    private function handlePaymentSucceeded($paymentIntent)
    {
        $payment = Payment::where('payment_intent_id', $paymentIntent->id)->first();

        if ($payment && !$payment->isSuccessful()) {
            $payment->markAsSucceeded($paymentIntent->charges->data[0]->id ?? null);

            // Update booking
            $payment->booking->update([
                'payment_status' => 'paid',
                'payment_method' => $paymentIntent->payment_method_types[0] ?? 'card',
            ]);

            // Store payment method details
            if (isset($paymentIntent->charges->data[0]->payment_method_details)) {
                $payment->update([
                    'payment_method_details' => $paymentIntent->charges->data[0]->payment_method_details,
                ]);
            }

            Log::info("Payment succeeded for booking {$payment->booking_id}");
        }
    }

    private function handlePaymentFailed($paymentIntent)
    {
        $payment = Payment::where('payment_intent_id', $paymentIntent->id)->first();

        if ($payment && !$payment->isFailed()) {
            $payment->markAsFailed($paymentIntent->last_payment_error->message ?? 'Payment failed');

            Log::info("Payment failed for booking {$payment->booking_id}");
        }
    }

    private function handleChargeDispute($dispute)
    {
        // Handle charge disputes
        Log::warning("Charge dispute created: {$dispute->id}");
        // Add your dispute handling logic here
    }

    public function refund(Request $request, Payment $payment)
    {
        $validated = $request->validate([
            'amount' => 'nullable|numeric|min:0.01|max:' . $payment->amount,
            'reason' => 'nullable|string|max:500',
        ]);

        if (!$payment->canBeRefunded()) {
            return redirect()->back()->with('error', 'This payment cannot be refunded.');
        }

        try {
            $refundAmount = $validated['amount'] ?? $payment->amount;

            // Create Stripe refund
            $refund = \Stripe\Refund::create([
                'charge' => $payment->stripe_charge_id,
                'amount' => $refundAmount * 100, // Convert to cents
                'reason' => 'requested_by_customer',
                'metadata' => [
                    'booking_id' => $payment->booking_id,
                    'reason' => $validated['reason'] ?? 'Refund requested',
                ],
            ]);

            // Create refund payment record
            Payment::create([
                'booking_id' => $payment->booking_id,
                'customer_id' => $payment->customer_id,
                'payment_intent_id' => $payment->payment_intent_id,
                'amount' => -$refundAmount, // Negative amount for refund
                'currency' => $payment->currency,
                'status' => Payment::STATUS_SUCCEEDED,
                'type' => Payment::TYPE_REFUND,
                'stripe_refund_id' => $refund->id,
                'metadata' => [
                    'original_payment_id' => $payment->id,
                    'reason' => $validated['reason'] ?? 'Refund requested',
                ],
            ]);

            // Update original payment status
            if ($refundAmount >= $payment->amount) {
                $payment->markAsRefunded($refund->id);
                $payment->booking->update(['payment_status' => 'refunded']);
            } else {
                $payment->update(['status' => Payment::STATUS_PARTIALLY_REFUNDED]);
                $payment->booking->update(['payment_status' => 'partial']);
            }

            return redirect()->back()->with('success', 'Refund processed successfully.');

        } catch (\Exception $e) {
            Log::error('Refund Failed: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Refund processing failed.');
        }
    }

    public function paymentHistory(Request $request)
    {
        $query = Payment::with(['booking', 'customer']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('payment_intent_id', 'like', "%{$request->search}%")
                  ->orWhereHas('booking', function ($bookingQuery) use ($request) {
                      $bookingQuery->where('booking_number', 'like', "%{$request->search}%");
                  })
                  ->orWhereHas('customer', function ($customerQuery) use ($request) {
                      $customerQuery->where('first_name', 'like', "%{$request->search}%")
                                   ->orWhere('last_name', 'like', "%{$request->search}%");
                  });
            });
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(20)->withQueryString();

        return view('payments.history', compact('payments'));
    }
}
```

## 🛠 Step 4: Adding Payment Routes

Add the payment routes to `routes/web.php`:

```php
// Payment routes - accessible by staff and admin
Route::middleware(['auth', 'role:staff,admin'])->group(function () {
    Route::get('/payments/history', [PaymentController::class, 'paymentHistory'])->name('payments.history');
    Route::post('/payments/{payment}/refund', [PaymentController::class, 'refund'])->name('payments.refund');
});

// Payment processing routes - accessible by all authenticated users
Route::middleware(['auth'])->group(function () {
    Route::post('/payments/create-intent', [PaymentController::class, 'createPaymentIntent'])->name('payments.create-intent');
    Route::post('/payments/confirm', [PaymentController::class, 'confirmPayment'])->name('payments.confirm');
});

// Webhook route - no authentication required
Route::post('/stripe/webhook', [PaymentController::class, 'handleWebhook'])->name('stripe.webhook');
```

## 🛠 Step 5: Updating Booking Model

Add payment relationship to the Booking model. Edit `app/Models/Booking.php`:

```php
// Add to the Booking model relationships section
public function payments(): HasMany
{
    return $this->hasMany(Payment::class);
}

public function successfulPayments(): HasMany
{
    return $this->hasMany(Payment::class)->where('status', Payment::STATUS_SUCCEEDED);
}

// Add helper methods
public function getTotalPaidAttribute(): float
{
    return $this->successfulPayments()
                ->where('type', Payment::TYPE_PAYMENT)
                ->sum('amount');
}

public function getTotalRefundedAttribute(): float
{
    return $this->payments()
                ->where('type', Payment::TYPE_REFUND)
                ->where('status', Payment::STATUS_SUCCEEDED)
                ->sum('amount');
}

public function hasSuccessfulPayment(): bool
{
    return $this->successfulPayments()->exists();
}
```

## 🧪 Testing the Payment System

1. **Test Payment Intent Creation**:
   - Create a booking
   - Generate payment intent
   - Verify Stripe dashboard shows the intent

2. **Test Payment Processing**:
   - Use Stripe test cards
   - Process successful payments
   - Test failed payments

3. **Test Webhooks**:
   - Configure webhook endpoint in Stripe
   - Test payment success webhooks
   - Verify payment status updates

4. **Test Refunds**:
   - Process refunds through admin interface
   - Verify refund records creation
   - Check Stripe dashboard for refunds

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Installed and configured Stripe for Laravel
✅ Created Payment model with comprehensive tracking
✅ Built secure payment processing workflows
✅ Implemented payment confirmations and webhooks
✅ Added payment history and refund management
✅ Created payment security and fraud protection
✅ Built payment-related notifications

### Payment Features Implemented:
- **Stripe Integration**: Secure payment processing with Stripe
- **Payment Tracking**: Comprehensive payment history and status
- **Webhook Handling**: Real-time payment status updates
- **Refund Management**: Full and partial refund capabilities
- **Security**: Webhook signature verification and secure processing
- **Payment Methods**: Support for multiple payment types
- **Fee Calculation**: Automatic Stripe fee calculation and tracking

## 🚀 What's Next?

In the next chapter, we'll:
- Build advanced reporting with charts and analytics
- Create revenue tracking and forecasting
- Add customer analytics and insights
- Implement performance metrics and KPIs
- Create exportable reports and data visualization

---

**Ready for advanced analytics?** Let's move on to [Chapter 9: Advanced Reporting and Analytics](./09-advanced-reporting-analytics.md)!
