# Chapter 14: POS (Point of Sale) Module

Welcome to Chapter 14! In this chapter, we'll build a comprehensive Point of Sale (POS) system that integrates seamlessly with our existing car wash management system, providing real-time transaction processing, receipt generation, and cash drawer management.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Build a real-time transaction processing interface
- Implement product/service selection with dynamic pricing
- Add support for multiple payment methods (cash, card, digital)
- Create receipt generation and printing functionality
- Build daily sales reporting and cash drawer management
- Integrate POS with existing booking and service systems
- Add inventory tracking for retail products
- Implement discount and promotion management

## 📋 What We'll Cover

1. Setting up POS database structure
2. Creating POS transaction models
3. Building the POS interface
4. Implementing payment processing
5. Receipt generation and printing
6. Cash drawer management
7. Sales reporting and analytics
8. Integration with existing systems

## 🛠 Step 1: Database Structure for POS

First, let's create the necessary migrations for our POS system:

```bash
# Create POS-related migrations
php artisan make:migration create_pos_transactions_table
php artisan make:migration create_pos_transaction_items_table
php artisan make:migration create_products_table
php artisan make:migration create_cash_drawers_table
php artisan make:migration create_pos_sessions_table
php artisan make:migration create_discounts_table
```

Edit `database/migrations/create_pos_transactions_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pos_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_number')->unique();
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Cashier
            $table->foreignId('pos_session_id')->constrained()->onDelete('cascade');
            $table->decimal('subtotal', 10, 2);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('amount_paid', 10, 2);
            $table->decimal('change_amount', 10, 2)->default(0);
            $table->enum('payment_method', ['cash', 'card', 'digital_wallet', 'bank_transfer', 'mixed']);
            $table->enum('status', ['pending', 'completed', 'cancelled', 'refunded'])->default('pending');
            $table->json('payment_details')->nullable(); // Store payment method details
            $table->text('notes')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            $table->index(['transaction_number']);
            $table->index(['customer_id', 'status']);
            $table->index(['user_id', 'created_at']);
            $table->index(['pos_session_id']);
            $table->index(['status', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pos_transactions');
    }
};
```

Edit `database/migrations/create_pos_transaction_items_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pos_transaction_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pos_transaction_id')->constrained()->onDelete('cascade');
            $table->string('item_type'); // 'service' or 'product'
            $table->unsignedBigInteger('item_id'); // service_id or product_id
            $table->string('item_name'); // Store name for historical purposes
            $table->decimal('unit_price', 10, 2);
            $table->integer('quantity');
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('total_price', 10, 2);
            $table->json('item_details')->nullable(); // Store additional item details
            $table->timestamps();

            $table->index(['pos_transaction_id']);
            $table->index(['item_type', 'item_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pos_transaction_items');
    }
};
```

Edit `database/migrations/create_products_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('sku')->unique();
            $table->text('description')->nullable();
            $table->string('category');
            $table->decimal('price', 10, 2);
            $table->decimal('cost', 10, 2)->nullable();
            $table->integer('stock_quantity')->default(0);
            $table->integer('min_stock_level')->default(0);
            $table->string('unit')->default('piece'); // piece, bottle, liter, etc.
            $table->string('barcode')->nullable()->unique();
            $table->string('image_path')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('track_inventory')->default(true);
            $table->json('attributes')->nullable(); // Size, color, etc.
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index(['sku']);
            $table->index(['barcode']);
            $table->index(['stock_quantity', 'min_stock_level']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
```

Edit `database/migrations/create_cash_drawers_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cash_drawers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('location');
            $table->decimal('opening_balance', 10, 2)->default(0);
            $table->decimal('current_balance', 10, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->json('denominations')->nullable(); // Track cash denominations
            $table->timestamps();

            $table->index(['is_active']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cash_drawers');
    }
};
```

Edit `database/migrations/create_pos_sessions_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pos_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Cashier
            $table->foreignId('cash_drawer_id')->constrained()->onDelete('cascade');
            $table->decimal('opening_balance', 10, 2);
            $table->decimal('closing_balance', 10, 2)->nullable();
            $table->decimal('expected_balance', 10, 2)->nullable();
            $table->decimal('cash_sales', 10, 2)->default(0);
            $table->decimal('card_sales', 10, 2)->default(0);
            $table->decimal('digital_sales', 10, 2)->default(0);
            $table->integer('transaction_count')->default(0);
            $table->timestamp('opened_at');
            $table->timestamp('closed_at')->nullable();
            $table->enum('status', ['open', 'closed'])->default('open');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['cash_drawer_id', 'status']);
            $table->index(['opened_at', 'closed_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pos_sessions');
    }
};
```

Edit `database/migrations/create_discounts_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('discounts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['percentage', 'fixed_amount']);
            $table->decimal('value', 10, 2);
            $table->decimal('minimum_amount', 10, 2)->nullable();
            $table->decimal('maximum_discount', 10, 2)->nullable();
            $table->integer('usage_limit')->nullable();
            $table->integer('used_count')->default(0);
            $table->boolean('is_active')->default(true);
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->json('applicable_items')->nullable(); // Services/products this applies to
            $table->timestamps();

            $table->index(['code', 'is_active']);
            $table->index(['start_date', 'end_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('discounts');
    }
};
```

Run the migrations:

```bash
php artisan migrate
```

## 🛠 Step 2: Creating POS Models

Create the models for our POS system:

```bash
# Create POS models
php artisan make:model PosTransaction
php artisan make:model PosTransactionItem
php artisan make:model Product
php artisan make:model CashDrawer
php artisan make:model PosSession
php artisan make:model Discount
```

Edit `app/Models/PosTransaction.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PosTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_number',
        'customer_id',
        'user_id',
        'pos_session_id',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'amount_paid',
        'change_amount',
        'payment_method',
        'status',
        'payment_details',
        'notes',
        'completed_at',
    ];

    protected $casts = [
        'payment_details' => 'array',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'change_amount' => 'decimal:2',
        'completed_at' => 'datetime',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';

    // Payment method constants
    const PAYMENT_CASH = 'cash';
    const PAYMENT_CARD = 'card';
    const PAYMENT_DIGITAL_WALLET = 'digital_wallet';
    const PAYMENT_BANK_TRANSFER = 'bank_transfer';
    const PAYMENT_MIXED = 'mixed';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_number)) {
                $transaction->transaction_number = static::generateTransactionNumber();
            }
        });
    }

    public static function generateTransactionNumber(): string
    {
        $prefix = 'POS';
        $date = now()->format('Ymd');
        $lastTransaction = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastTransaction ? 
            intval(substr($lastTransaction->transaction_number, -4)) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function posSession(): BelongsTo
    {
        return $this->belongsTo(PosSession::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(PosTransactionItem::class);
    }

    // Accessors
    public function getFormattedTotalAttribute(): string
    {
        return '$' . number_format($this->total_amount, 2);
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_REFUNDED => 'Refunded',
            default => 'Unknown',
        };
    }

    public function getPaymentMethodLabelAttribute(): string
    {
        return match($this->payment_method) {
            self::PAYMENT_CASH => 'Cash',
            self::PAYMENT_CARD => 'Card',
            self::PAYMENT_DIGITAL_WALLET => 'Digital Wallet',
            self::PAYMENT_BANK_TRANSFER => 'Bank Transfer',
            self::PAYMENT_MIXED => 'Mixed Payment',
            default => 'Unknown',
        };
    }

    // Methods
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum('total_price');
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->change_amount = max(0, $this->amount_paid - $this->total_amount);
    }

    public function markAsCompleted(): void
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'completed_at' => now(),
        ]);
    }

    public function canBeRefunded(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }
}
```

## 🧪 Testing the POS System

1. **Test Transaction Processing**:
   - Create transactions with different payment methods
   - Test inventory updates for products
   - Verify receipt generation

2. **Test Cash Drawer Management**:
   - Open and close POS sessions
   - Track cash flow and balancing
   - Generate session reports

3. **Test Integration**:
   - Verify integration with existing booking system
   - Test customer data synchronization
   - Validate reporting accuracy

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Built a comprehensive POS system with real-time transaction processing
✅ Implemented product/service selection with dynamic pricing
✅ Added support for multiple payment methods
✅ Created receipt generation and printing functionality
✅ Built daily sales reporting and cash drawer management
✅ Integrated POS with existing booking and service systems
✅ Added inventory tracking for retail products
✅ Implemented discount and promotion management

### POS Features Implemented:
- **Real-time Transaction Processing**: Fast and efficient checkout process
- **Multi-payment Support**: Cash, card, digital wallet, and bank transfer options
- **Inventory Management**: Automatic stock updates and low-stock alerts
- **Receipt System**: Professional receipt generation with printing support
- **Cash Drawer Management**: Complete session tracking and balancing
- **Discount System**: Flexible promotion and discount code management
- **Integration**: Seamless integration with existing car wash systems
- **Reporting**: Comprehensive sales analytics and reporting

## 🚀 What's Next?

In the next chapter, we'll:
- Build a digital queue management system
- Create real-time queue status displays
- Implement estimated wait times
- Add digital signage for customer information
- Integrate queue system with bookings

---

**Ready for queue management?** Let's move on to [Chapter 15: Queue Display System](./15-queue-display-system.md)!
